# PowerShell script to add analysis sheet to Excel file
param(
    [string]$ExcelFile = ""
)

# Find Excel file if not specified
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found in current directory" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
    Write-Host "Using Excel file: $ExcelFile" -ForegroundColor Yellow
}

if (-not (Test-Path $ExcelFile)) {
    Write-Host "Error: Excel file not found: $ExcelFile" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    
    # Read original data for analysis
    $originalSheet = $workbook.ActiveSheet
    Write-Host "Reading data from original sheet: $($originalSheet.Name)" -ForegroundColor Yellow
    
    $lastRow = $originalSheet.UsedRange.Rows.Count
    
    # Read column D data (starting from row 2, skip header)
    $columnData = @()
    $emptyCount = 0
    
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        } else {
            $emptyCount++
        }
    }
    
    if ($columnData.Count -eq 0) {
        Write-Host "No valid data in column D" -ForegroundColor Red
        $workbook.Close($false)
        $excel.Quit()
        exit 1
    }
    
    # Count values
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    
    Write-Host "Analysis completed. Creating new sheet..." -ForegroundColor Green
    
    # Check if analysis sheet exists and delete it
    $sheetName = "Analysis"
    $existingSheet = $null
    foreach ($sheet in $workbook.Worksheets) {
        if ($sheet.Name -eq $sheetName) {
            $existingSheet = $sheet
            break
        }
    }

    if ($existingSheet -ne $null) {
        Write-Host "Deleting existing analysis sheet..." -ForegroundColor Yellow
        $existingSheet.Delete()
    }
    
    # Create new worksheet
    $newSheet = $workbook.Worksheets.Add()
    $newSheet.Name = $sheetName
    
    Write-Host "Created new sheet: $sheetName" -ForegroundColor Green
    
    # Set title and basic information
    $newSheet.Cells.Item(1, 1).Value2 = "Column D Photography Type Analysis Report"
    $newSheet.Cells.Item(1, 1).Font.Size = 16
    $newSheet.Cells.Item(1, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(3, 1).Value2 = "Analysis Time:"
    $newSheet.Cells.Item(3, 2).Value2 = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    
    $newSheet.Cells.Item(4, 1).Value2 = "Data Source:"
    $newSheet.Cells.Item(4, 2).Value2 = $ExcelFile
    
    $newSheet.Cells.Item(5, 1).Value2 = "Original Sheet:"
    $newSheet.Cells.Item(5, 2).Value2 = $originalSheet.Name
    
    # Data overview
    $newSheet.Cells.Item(7, 1).Value2 = "Data Overview"
    $newSheet.Cells.Item(7, 1).Font.Size = 14
    $newSheet.Cells.Item(7, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(8, 1).Value2 = "Total Records:"
    $newSheet.Cells.Item(8, 2).Value2 = $totalCount
    
    $newSheet.Cells.Item(9, 1).Value2 = "Unique Values:"
    $newSheet.Cells.Item(9, 2).Value2 = $valueCount.Count
    
    $newSheet.Cells.Item(10, 1).Value2 = "Empty Cells:"
    $newSheet.Cells.Item(10, 2).Value2 = $emptyCount
    
    # Detailed statistics table
    $startRow = 13
    $newSheet.Cells.Item($startRow, 1).Value2 = "Detailed Statistics"
    $newSheet.Cells.Item($startRow, 1).Font.Size = 14
    $newSheet.Cells.Item($startRow, 1).Font.Bold = $true
    
    # Table header
    $headerRow = $startRow + 2
    $newSheet.Cells.Item($headerRow, 1).Value2 = "Rank"
    $newSheet.Cells.Item($headerRow, 2).Value2 = "Photography Type"
    $newSheet.Cells.Item($headerRow, 3).Value2 = "Count"
    $newSheet.Cells.Item($headerRow, 4).Value2 = "Percentage"
    
    # Format header
    $headerRange = $newSheet.Range($newSheet.Cells.Item($headerRow, 1), $newSheet.Cells.Item($headerRow, 4))
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # Light blue background
    $headerRange.Borders.LineStyle = 1
    
    # Fill in statistical data
    $currentRow = $headerRow + 1
    $rank = 1
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($currentRow, 1).Value2 = $rank
        $newSheet.Cells.Item($currentRow, 2).Value2 = $item.Key
        $newSheet.Cells.Item($currentRow, 3).Value2 = $item.Value
        $newSheet.Cells.Item($currentRow, 4).Value2 = $percentage
        
        # Format data
        $dataRange = $newSheet.Range($newSheet.Cells.Item($currentRow, 1), $newSheet.Cells.Item($currentRow, 4))
        $dataRange.Borders.LineStyle = 1
        
        # Highlight top 10
        if ($rank -le 10) {
            $dataRange.Interior.Color = 16777164  # Light yellow background
        }
        
        $currentRow++
        $rank++
    }
    
    # Add TOP 10 summary
    $summaryStartRow = $headerRow + $sortedResults.Count + 3
    $newSheet.Cells.Item($summaryStartRow, 1).Value2 = "TOP 10 Summary"
    $newSheet.Cells.Item($summaryStartRow, 1).Font.Size = 14
    $newSheet.Cells.Item($summaryStartRow, 1).Font.Bold = $true
    
    $summaryHeaderRow = $summaryStartRow + 2
    $newSheet.Cells.Item($summaryHeaderRow, 1).Value2 = "Rank"
    $newSheet.Cells.Item($summaryHeaderRow, 2).Value2 = "Photography Type"
    $newSheet.Cells.Item($summaryHeaderRow, 3).Value2 = "Count"
    $newSheet.Cells.Item($summaryHeaderRow, 4).Value2 = "Percentage"
    
    # Format TOP 10 header
    $summaryHeaderRange = $newSheet.Range($newSheet.Cells.Item($summaryHeaderRow, 1), $newSheet.Cells.Item($summaryHeaderRow, 4))
    $summaryHeaderRange.Font.Bold = $true
    $summaryHeaderRange.Interior.Color = 5287936  # Green background
    $summaryHeaderRange.Borders.LineStyle = 1
    
    # Fill TOP 10 data
    $summaryCurrentRow = $summaryHeaderRow + 1
    $top10 = $sortedResults | Select-Object -First 10
    $rank = 1
    
    foreach ($item in $top10) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($summaryCurrentRow, 1).Value2 = $rank
        $newSheet.Cells.Item($summaryCurrentRow, 2).Value2 = $item.Key
        $newSheet.Cells.Item($summaryCurrentRow, 3).Value2 = $item.Value
        $newSheet.Cells.Item($summaryCurrentRow, 4).Value2 = $percentage
        
        # Format data
        $summaryDataRange = $newSheet.Range($newSheet.Cells.Item($summaryCurrentRow, 1), $newSheet.Cells.Item($summaryCurrentRow, 4))
        $summaryDataRange.Borders.LineStyle = 1
        $summaryDataRange.Interior.Color = 13434828  # Light green background
        
        $summaryCurrentRow++
        $rank++
    }
    
    # Auto-fit columns
    $newSheet.Columns("A:D").AutoFit()
    
    # Set column width for better display
    $newSheet.Columns("B").ColumnWidth = 40  # Photography type column wider
    
    # Activate new sheet
    $newSheet.Activate()
    
    # Save workbook
    $workbook.Save()
    
    Write-Host "Successfully added analysis sheet to Excel file!" -ForegroundColor Green
    Write-Host "Sheet name: $sheetName" -ForegroundColor Yellow
    Write-Host "Total records analyzed: $totalCount" -ForegroundColor Yellow
    Write-Host "Unique photography types: $($valueCount.Count)" -ForegroundColor Yellow
    
    # Close Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error during processing: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {
        # Ignore cleanup errors
    }
    
    exit 1
}

Write-Host ""
Write-Host "Analysis sheet creation completed!" -ForegroundColor Green
