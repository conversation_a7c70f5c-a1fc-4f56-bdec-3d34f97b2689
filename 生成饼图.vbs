' 直接生成包含饼图的Excel文件
' 双击此文件即可运行

Dim excel, workbook, worksheet, chart, chartObject, dataRange
Dim outputFile

On Error Resume Next

' 创建Excel应用程序
Set excel = CreateObject("Excel.Application")
If Err.Number <> 0 Then
    MsgBox "错误：无法启动Excel。请确保已安装Microsoft Excel。", vbCritical, "错误"
    WScript.Quit
End If

excel.Visible = False
excel.DisplayAlerts = False

' 创建工作簿
Set workbook = excel.Workbooks.Add()
Set worksheet = workbook.ActiveSheet
worksheet.Name = "摄影分类统计"

' 添加标题
worksheet.Cells(1, 1).Value = "摄影分类统计分析（拆分版）"
worksheet.Cells(1, 1).Font.Size = 16
worksheet.Cells(1, 1).Font.Bold = True

' 添加说明
worksheet.Cells(2, 1).Value = "分析时间: 2025年7月26日"
worksheet.Cells(3, 1).Value = "数据说明: 多分类条目已拆分统计"
worksheet.Cells(4, 1).Value = "总分类数: 1986个实例"

' 添加表格标题
worksheet.Cells(6, 1).Value = "摄影分类"
worksheet.Cells(6, 2).Value = "计数"
worksheet.Cells(6, 3).Value = "占比(%)"
worksheet.Cells(6, 1).Font.Bold = True
worksheet.Cells(6, 2).Font.Bold = True
worksheet.Cells(6, 3).Font.Bold = True

' 添加数据（TOP 12）
worksheet.Cells(7, 1).Value = "人像摄影"
worksheet.Cells(7, 2).Value = 534
worksheet.Cells(7, 3).Value = 26.89

worksheet.Cells(8, 1).Value = "婚礼摄影"
worksheet.Cells(8, 2).Value = 145
worksheet.Cells(8, 3).Value = 7.30

worksheet.Cells(9, 1).Value = "其他"
worksheet.Cells(9, 2).Value = 136
worksheet.Cells(9, 3).Value = 6.85

worksheet.Cells(10, 1).Value = "商业摄影"
worksheet.Cells(10, 2).Value = 118
worksheet.Cells(10, 3).Value = 5.94

worksheet.Cells(11, 1).Value = "儿童摄影"
worksheet.Cells(11, 2).Value = 117
worksheet.Cells(11, 3).Value = 5.89

worksheet.Cells(12, 1).Value = "风景摄影"
worksheet.Cells(12, 2).Value = 99
worksheet.Cells(12, 3).Value = 4.99

worksheet.Cells(13, 1).Value = "婚礼跟拍"
worksheet.Cells(13, 2).Value = 69
worksheet.Cells(13, 3).Value = 3.47

worksheet.Cells(14, 1).Value = "风光人文"
worksheet.Cells(14, 2).Value = 62
worksheet.Cells(14, 3).Value = 3.12

worksheet.Cells(15, 1).Value = "视频制作"
worksheet.Cells(15, 2).Value = 52
worksheet.Cells(15, 3).Value = 2.62

worksheet.Cells(16, 1).Value = "儿童亲子"
worksheet.Cells(16, 2).Value = 40
worksheet.Cells(16, 3).Value = 2.01

worksheet.Cells(17, 1).Value = "未分类"
worksheet.Cells(17, 2).Value = 37
worksheet.Cells(17, 3).Value = 1.86

worksheet.Cells(18, 1).Value = "自媒体VLOG"
worksheet.Cells(18, 2).Value = 28
worksheet.Cells(18, 3).Value = 1.41

' 设置列宽
worksheet.Columns("A:A").ColumnWidth = 18
worksheet.Columns("B:B").ColumnWidth = 8
worksheet.Columns("C:C").ColumnWidth = 10

' 创建饼图
Set dataRange = worksheet.Range("A6:B18")
Set chartObject = worksheet.Shapes.AddChart()
Set chart = chartObject.Chart

' 配置图表
chart.ChartType = 5  ' xlPie
chart.SetSourceData dataRange
chart.HasTitle = True
chart.ChartTitle.Text = "摄影分类分布统计"

' 设置图表位置
chartObject.Left = 250
chartObject.Top = 120
chartObject.Width = 450
chartObject.Height = 350

' 添加数据标签
chart.SeriesCollection(1).HasDataLabels = True
chart.SeriesCollection(1).DataLabels.ShowPercentage = True
chart.SeriesCollection(1).DataLabels.ShowValue = False
chart.SeriesCollection(1).DataLabels.ShowCategoryName = False

' 设置图例
chart.HasLegend = True
chart.Legend.Position = -4152  ' xlLegendPositionRight

' 添加统计摘要
worksheet.Cells(20, 1).Value = "统计摘要:"
worksheet.Cells(20, 1).Font.Bold = True
worksheet.Cells(20, 1).Font.Size = 12

worksheet.Cells(21, 1).Value = "• 人像摄影占主导地位 (26.9%)"
worksheet.Cells(22, 1).Value = "• 前5类占总数的53.9%"
worksheet.Cells(23, 1).Value = "• 显示多元化发展趋势"
worksheet.Cells(24, 1).Value = "• 复合型摄影师比例较高"

' 保存文件
outputFile = "摄影分类统计分析_含饼图.xlsx"
workbook.SaveAs CreateObject("Scripting.FileSystemObject").GetAbsolutePathName(outputFile)

If Err.Number = 0 Then
    MsgBox "成功创建Excel文件！" & vbCrLf & vbCrLf & _
           "文件名: " & outputFile & vbCrLf & _
           "包含内容:" & vbCrLf & _
           "✓ 完整的数据表格 (TOP 12分类)" & vbCrLf & _
           "✓ 饼图 (带百分比标签)" & vbCrLf & _
           "✓ 图例和统计摘要" & vbCrLf & vbCrLf & _
           "TOP 5 分类:" & vbCrLf & _
           "1. 人像摄影: 534次 (26.9%)" & vbCrLf & _
           "2. 婚礼摄影: 145次 (7.3%)" & vbCrLf & _
           "3. 其他: 136次 (6.9%)" & vbCrLf & _
           "4. 商业摄影: 118次 (5.9%)" & vbCrLf & _
           "5. 儿童摄影: 117次 (5.9%)", vbInformation, "任务完成"
Else
    MsgBox "保存文件时出现错误: " & Err.Description, vbCritical, "错误"
End If

' 关闭Excel
workbook.Close False
excel.Quit

' 清理对象
Set chart = Nothing
Set chartObject = Nothing
Set dataRange = Nothing
Set worksheet = Nothing
Set workbook = Nothing
Set excel = Nothing
