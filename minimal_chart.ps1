$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$workbook = $excel.Workbooks.Add()
$worksheet = $workbook.ActiveSheet

$worksheet.Cells.Item(1, 1) = "Photography Analysis"
$worksheet.Cells.Item(2, 1) = "Category"
$worksheet.Cells.Item(2, 2) = "Count"
$worksheet.Cells.Item(3, 1) = "Portrait"
$worksheet.Cells.Item(3, 2) = 534
$worksheet.Cells.Item(4, 1) = "Wedding"
$worksheet.Cells.Item(4, 2) = 145
$worksheet.Cells.Item(5, 1) = "Commercial"
$worksheet.Cells.Item(5, 2) = 118
$worksheet.Cells.Item(6, 1) = "Children"
$worksheet.Cells.Item(6, 2) = 117

$range = $worksheet.Range("A2:B6")
$chart = $worksheet.Shapes.AddChart().Chart
$chart.ChartType = 5
$chart.SetSourceData($range)
$chart.HasTitle = $true
$chart.ChartTitle.Text = "Photography Distribution"

$outputPath = Join-Path (Get-Location) "PhotoChart.xlsx"
$workbook.SaveAs($outputPath)
$workbook.Close($false)
$excel.Quit()

Write-Host "Created: PhotoChart.xlsx"
