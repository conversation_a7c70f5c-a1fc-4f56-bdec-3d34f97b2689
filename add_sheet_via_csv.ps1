# PowerShell script to add analysis sheet via CSV import
param(
    [string]$ExcelFile = ""
)

# Find Excel file
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $originalSheet = $workbook.ActiveSheet
    
    # Read data from column D
    $lastRow = $originalSheet.UsedRange.Rows.Count
    $columnData = @()
    
    Write-Host "Reading data from column D..." -ForegroundColor Yellow
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        }
    }
    
    # Count values
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    
    Write-Host "Data analysis completed. Total records: $totalCount" -ForegroundColor Yellow
    
    # Check if analysis sheet exists and delete it
    $sheetName = "Analysis"
    foreach ($sheet in $workbook.Worksheets) {
        if ($sheet.Name -eq $sheetName) {
            Write-Host "Deleting existing analysis sheet..." -ForegroundColor Yellow
            $sheet.Delete()
            break
        }
    }
    
    # Create new sheet
    $newSheet = $workbook.Worksheets.Add()
    $newSheet.Name = $sheetName
    
    Write-Host "Created new sheet: $sheetName" -ForegroundColor Green
    
    # Add basic information
    $newSheet.Cells.Item(1, 1) = "Photography Type Analysis Report"
    $newSheet.Cells.Item(1, 1).Font.Size = 16
    $newSheet.Cells.Item(1, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(3, 1) = "Analysis Date:"
    $newSheet.Cells.Item(3, 2) = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
    
    $newSheet.Cells.Item(4, 1) = "Data Source:"
    $newSheet.Cells.Item(4, 2) = $ExcelFile
    
    $newSheet.Cells.Item(5, 1) = "Original Sheet:"
    $newSheet.Cells.Item(5, 2) = $originalSheet.Name
    
    # Data overview
    $newSheet.Cells.Item(7, 1) = "Data Overview"
    $newSheet.Cells.Item(7, 1).Font.Size = 14
    $newSheet.Cells.Item(7, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(8, 1) = "Total Records:"
    $newSheet.Cells.Item(8, 2) = $totalCount
    
    $newSheet.Cells.Item(9, 1) = "Unique Types:"
    $newSheet.Cells.Item(9, 2) = $valueCount.Count
    
    # Table headers
    $headerRow = 12
    $newSheet.Cells.Item($headerRow, 1) = "Rank"
    $newSheet.Cells.Item($headerRow, 2) = "Photography Type"
    $newSheet.Cells.Item($headerRow, 3) = "Count"
    $newSheet.Cells.Item($headerRow, 4) = "Percentage"
    
    # Format headers
    for ($col = 1; $col -le 4; $col++) {
        $newSheet.Cells.Item($headerRow, $col).Font.Bold = $true
        $newSheet.Cells.Item($headerRow, $col).Interior.Color = 15123099  # Light blue
    }
    
    # Add data rows
    $currentRow = $headerRow + 1
    $rank = 1
    
    Write-Host "Adding data to sheet..." -ForegroundColor Yellow
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($currentRow, 1) = $rank
        $newSheet.Cells.Item($currentRow, 2) = $item.Key
        $newSheet.Cells.Item($currentRow, 3) = $item.Value
        $newSheet.Cells.Item($currentRow, 4) = $percentage
        
        # Highlight top 10
        if ($rank -le 10) {
            for ($col = 1; $col -le 4; $col++) {
                $newSheet.Cells.Item($currentRow, $col).Interior.Color = 16777164  # Light yellow
            }
        }
        
        $currentRow++
        $rank++
        
        # Progress indicator
        if ($rank % 10 -eq 0) {
            Write-Host "  Added $rank rows..." -ForegroundColor Gray
        }
    }
    
    # Auto-fit columns
    Write-Host "Formatting sheet..." -ForegroundColor Yellow
    $newSheet.Columns("A:A").ColumnWidth = 8
    $newSheet.Columns("B:B").ColumnWidth = 45
    $newSheet.Columns("C:C").ColumnWidth = 10
    $newSheet.Columns("D:D").ColumnWidth = 12
    
    # Activate the new sheet
    $newSheet.Activate()
    
    # Save workbook
    Write-Host "Saving workbook..." -ForegroundColor Yellow
    $workbook.Save()
    
    Write-Host "Successfully added analysis sheet!" -ForegroundColor Green
    Write-Host "Sheet name: $sheetName" -ForegroundColor Yellow
    Write-Host "Total records: $totalCount" -ForegroundColor Yellow
    Write-Host "Unique types: $($valueCount.Count)" -ForegroundColor Yellow
    
    # Show top 5
    Write-Host "`nTOP 5 Photography Types:" -ForegroundColor Cyan
    $top5 = $sortedResults | Select-Object -First 5
    $rank = 1
    foreach ($item in $top5) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        Write-Host "  $rank. $($item.Key): $($item.Value) ($percentage%)" -ForegroundColor White
        $rank++
    }
    
    # Close Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.ToString())" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}

Write-Host "`nTask completed successfully!" -ForegroundColor Green
