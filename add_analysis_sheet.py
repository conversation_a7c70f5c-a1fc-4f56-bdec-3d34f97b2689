#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在原Excel文件中添加D列统计分析的新sheet
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side
from collections import Counter
import os
import glob

def find_excel_file():
    """查找Excel文件"""
    excel_files = [f for f in glob.glob("*.xlsx") if not f.startswith("~")]
    if not excel_files:
        raise FileNotFoundError("未找到Excel文件")
    return excel_files[0]

def add_analysis_sheet():
    """在Excel文件中添加分析sheet"""
    try:
        # 查找Excel文件
        excel_file = find_excel_file()
        print(f"正在处理Excel文件: {excel_file}")
        
        # 打开工作簿
        workbook = openpyxl.load_workbook(excel_file)
        original_sheet = workbook.active
        
        print(f"原始工作表: {original_sheet.title}")
        
        # 读取D列数据
        column_data = []
        max_row = original_sheet.max_row
        
        for row in range(2, max_row + 1):  # 从第2行开始，跳过标题
            cell_value = original_sheet.cell(row=row, column=4).value
            if cell_value is not None and str(cell_value).strip():
                column_data.append(str(cell_value).strip())
        
        if not column_data:
            print("D列没有有效数据")
            return
        
        # 统计数据
        value_counts = Counter(column_data)
        total_count = len(column_data)
        
        print(f"数据分析完成。总记录数: {total_count}, 唯一值: {len(value_counts)}")
        
        # 删除已存在的分析sheet（如果有）
        sheet_name = "D列统计分析"
        if sheet_name in workbook.sheetnames:
            print("删除已存在的分析sheet...")
            workbook.remove(workbook[sheet_name])
        
        # 创建新的分析sheet
        analysis_sheet = workbook.create_sheet(title=sheet_name)
        print(f"创建新sheet: {sheet_name}")
        
        # 设置样式
        title_font = Font(size=16, bold=True)
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
        top10_fill = PatternFill(start_color="FFFFE0", end_color="FFFFE0", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 添加标题和基本信息
        analysis_sheet['A1'] = "D列摄影类型统计分析报告"
        analysis_sheet['A1'].font = title_font
        
        analysis_sheet['A3'] = "分析时间:"
        analysis_sheet['B3'] = "2025年7月26日"
        
        analysis_sheet['A4'] = "数据来源:"
        analysis_sheet['B4'] = excel_file
        
        analysis_sheet['A5'] = "原始工作表:"
        analysis_sheet['B5'] = original_sheet.title
        
        # 数据概览
        analysis_sheet['A7'] = "数据概览"
        analysis_sheet['A7'].font = Font(size=14, bold=True)
        
        analysis_sheet['A8'] = "总数据量:"
        analysis_sheet['B8'] = total_count
        
        analysis_sheet['A9'] = "唯一值数量:"
        analysis_sheet['B9'] = len(value_counts)
        
        analysis_sheet['A10'] = "空值数量:"
        analysis_sheet['B10'] = 0
        
        # 详细统计表格
        start_row = 13
        analysis_sheet[f'A{start_row}'] = "详细统计结果"
        analysis_sheet[f'A{start_row}'].font = Font(size=14, bold=True)
        
        # 表格标题
        header_row = start_row + 2
        headers = ["排名", "摄影类型", "数量", "占比(%)"]
        for col, header in enumerate(headers, 1):
            cell = analysis_sheet.cell(row=header_row, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
        
        # 填入数据
        current_row = header_row + 1
        for rank, (value, count) in enumerate(value_counts.most_common(), 1):
            percentage = round(count / total_count * 100, 2)
            
            # 排名
            cell = analysis_sheet.cell(row=current_row, column=1)
            cell.value = rank
            cell.border = border
            
            # 摄影类型
            cell = analysis_sheet.cell(row=current_row, column=2)
            cell.value = value
            cell.border = border
            
            # 数量
            cell = analysis_sheet.cell(row=current_row, column=3)
            cell.value = count
            cell.border = border
            
            # 占比
            cell = analysis_sheet.cell(row=current_row, column=4)
            cell.value = percentage
            cell.border = border
            
            # 为前10名设置特殊背景色
            if rank <= 10:
                for col in range(1, 5):
                    analysis_sheet.cell(row=current_row, column=col).fill = top10_fill
            
            current_row += 1
        
        # TOP 10汇总表
        summary_start_row = current_row + 2
        analysis_sheet[f'A{summary_start_row}'] = "TOP 10 摄影类型汇总"
        analysis_sheet[f'A{summary_start_row}'].font = Font(size=14, bold=True)
        
        summary_header_row = summary_start_row + 2
        green_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
        
        for col, header in enumerate(headers, 1):
            cell = analysis_sheet.cell(row=summary_header_row, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = green_fill
            cell.border = border
        
        # 填入TOP 10数据
        summary_current_row = summary_header_row + 1
        light_green_fill = PatternFill(start_color="E0FFE0", end_color="E0FFE0", fill_type="solid")
        
        for rank, (value, count) in enumerate(value_counts.most_common(10), 1):
            percentage = round(count / total_count * 100, 2)
            
            cells_data = [rank, value, count, percentage]
            for col, data in enumerate(cells_data, 1):
                cell = analysis_sheet.cell(row=summary_current_row, column=col)
                cell.value = data
                cell.border = border
                cell.fill = light_green_fill
            
            summary_current_row += 1
        
        # 调整列宽
        analysis_sheet.column_dimensions['A'].width = 8
        analysis_sheet.column_dimensions['B'].width = 45
        analysis_sheet.column_dimensions['C'].width = 10
        analysis_sheet.column_dimensions['D'].width = 12
        
        # 激活分析sheet
        workbook.active = analysis_sheet
        
        # 保存工作簿
        workbook.save(excel_file)
        workbook.close()
        
        print("✅ 成功在Excel文件中添加了统计分析sheet!")
        print(f"📊 Sheet名称: {sheet_name}")
        print(f"📈 总记录数: {total_count}")
        print(f"🎯 唯一摄影类型: {len(value_counts)}")
        
        # 显示TOP 5
        print("\n🏆 TOP 5 摄影类型:")
        for rank, (value, count) in enumerate(value_counts.most_common(5), 1):
            percentage = round(count / total_count * 100, 2)
            print(f"  {rank}. {value}: {count}人 ({percentage}%)")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始在Excel文件中添加D列统计分析sheet...")
    success = add_analysis_sheet()
    if success:
        print("\n🎉 任务完成!")
    else:
        print("\n💥 任务失败!")
