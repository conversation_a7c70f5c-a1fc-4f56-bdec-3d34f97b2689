# Create Excel file with pie chart from split categories analysis
if (-not (Test-Path "split_categories_analysis.csv")) {
    Write-Host "Error: split_categories_analysis.csv not found." -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Creating Excel file with pie chart..." -ForegroundColor Green
    
    # Read CSV data
    $csvData = Import-Csv "split_categories_analysis.csv"
    
    # Create Excel application
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $true  # Make visible to see the chart creation
    $excel.DisplayAlerts = $false
    
    # Create new workbook
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "分类统计分析"
    
    Write-Host "Adding data to worksheet..." -ForegroundColor Yellow
    
    # Add title
    $worksheet.Cells.Item(1, 1) = "D列摄影分类统计分析（拆分版）"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Add metadata
    $worksheet.Cells.Item(3, 1) = "分析说明："
    $worksheet.Cells.Item(3, 2) = "每个条目中的多个分类都单独计算"
    
    $worksheet.Cells.Item(4, 1) = "分析时间："
    $worksheet.Cells.Item(4, 2) = (Get-Date).ToString("yyyy年MM月dd日 HH:mm:ss")
    
    $worksheet.Cells.Item(5, 1) = "总分类计数："
    $totalCount = ($csvData | Measure-Object -Property Count -Sum).Sum
    $worksheet.Cells.Item(5, 2) = $totalCount
    
    $worksheet.Cells.Item(6, 1) = "唯一分类数："
    $worksheet.Cells.Item(6, 2) = $csvData.Count
    
    # Add table headers
    $startRow = 9
    $worksheet.Cells.Item($startRow, 1) = "排名"
    $worksheet.Cells.Item($startRow, 2) = "摄影分类"
    $worksheet.Cells.Item($startRow, 3) = "计数"
    $worksheet.Cells.Item($startRow, 4) = "占比(%)"
    
    # Format headers
    $headerRange = $worksheet.Range("A$startRow" + ":D$startRow")
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # Light blue
    $headerRange.Borders.LineStyle = 1
    
    # Add data
    $currentRow = $startRow + 1
    foreach ($row in $csvData) {
        $worksheet.Cells.Item($currentRow, 1) = [int]$row.Rank
        $worksheet.Cells.Item($currentRow, 2) = $row.Category
        $worksheet.Cells.Item($currentRow, 3) = [int]$row.Count
        $worksheet.Cells.Item($currentRow, 4) = [double]$row.Percentage
        
        # Format data row
        $dataRange = $worksheet.Range("A$currentRow" + ":D$currentRow")
        $dataRange.Borders.LineStyle = 1
        
        # Highlight top 10
        if ([int]$row.Rank -le 10) {
            $dataRange.Interior.Color = 16777164  # Light yellow
        }
        
        $currentRow++
    }
    
    # Auto-fit columns
    $worksheet.Columns("A:A").ColumnWidth = 8
    $worksheet.Columns("B:B").ColumnWidth = 30
    $worksheet.Columns("C:C").ColumnWidth = 10
    $worksheet.Columns("D:D").ColumnWidth = 12
    
    Write-Host "Creating pie chart..." -ForegroundColor Yellow
    
    # Prepare chart data (top 12 categories to avoid overcrowding)
    $chartStartCol = 6  # Column F
    $chartStartRow = 9
    
    $worksheet.Cells.Item($chartStartRow, $chartStartCol) = "图表数据 (TOP 12)"
    $worksheet.Cells.Item($chartStartRow, $chartStartCol).Font.Bold = $true
    
    $chartHeaderRow = $chartStartRow + 1
    $worksheet.Cells.Item($chartHeaderRow, $chartStartCol) = "分类"
    $worksheet.Cells.Item($chartHeaderRow, $chartStartCol + 1) = "计数"
    
    # Add top 12 data for chart
    $chartDataRow = $chartHeaderRow + 1
    $top12 = $csvData | Select-Object -First 12
    
    foreach ($item in $top12) {
        $worksheet.Cells.Item($chartDataRow, $chartStartCol) = $item.Category
        $worksheet.Cells.Item($chartDataRow, $chartStartCol + 1) = [int]$item.Count
        $chartDataRow++
    }
    
    # Create pie chart
    $chartRange = $worksheet.Range($worksheet.Cells.Item($chartHeaderRow, $chartStartCol), $worksheet.Cells.Item($chartDataRow - 1, $chartStartCol + 1))
    
    # Add chart
    $chart = $worksheet.Shapes.AddChart2(201, 5).Chart  # xlPie = 5
    $chart.SetSourceData($chartRange)
    $chart.ChartTitle.Text = "摄影分类分布 (TOP 12)"
    $chart.ChartTitle.Font.Size = 14
    $chart.ChartTitle.Font.Bold = $true
    
    # Position chart
    $chart.Parent.Left = $worksheet.Cells.Item(12, 6).Left
    $chart.Parent.Top = $worksheet.Cells.Item(12, 6).Top
    $chart.Parent.Width = 450
    $chart.Parent.Height = 350
    
    # Format chart
    $chart.HasLegend = $true
    $chart.Legend.Position = -4152  # xlLegendPositionRight
    
    # Add data labels with percentages
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chart.SeriesCollection(1).DataLabels.ShowCategoryName = $false
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false
    $chart.SeriesCollection(1).DataLabels.Font.Size = 9
    
    Write-Host "Saving workbook..." -ForegroundColor Yellow
    
    # Save workbook
    $outputFile = "D列分类统计分析_拆分版_含饼图.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    $workbook.SaveAs($fullPath)
    
    Write-Host "Successfully created Excel file with pie chart: $outputFile" -ForegroundColor Green
    
    # Display summary
    Write-Host "`nAnalysis Summary:" -ForegroundColor Cyan
    Write-Host "Total category instances: $totalCount" -ForegroundColor White
    Write-Host "Unique categories: $($csvData.Count)" -ForegroundColor White
    Write-Host "Output file: $outputFile" -ForegroundColor White
    
    Write-Host "`nTOP 10 Categories:" -ForegroundColor Cyan
    $top10 = $csvData | Select-Object -First 10
    foreach ($item in $top10) {
        Write-Host "  $($item.Rank). $($item.Category): $($item.Count) ($($item.Percentage)%)" -ForegroundColor White
    }
    
    # Keep Excel visible for user to see the result
    Write-Host "`nExcel file is now open. You can review the chart and save/close when ready." -ForegroundColor Green
    
    # Don't close Excel automatically - let user review
    # $workbook.Close($false)
    # $excel.Quit()
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}

Write-Host "`nTask completed!" -ForegroundColor Green
