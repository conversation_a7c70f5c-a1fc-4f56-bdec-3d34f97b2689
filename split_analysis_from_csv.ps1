# Analyze split categories from existing CSV data
if (-not (Test-Path "column_d_results.csv")) {
    Write-Host "Error: column_d_results.csv not found. Please run the original analysis first." -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Reading existing analysis data..." -ForegroundColor Green
    
    # Read the CSV data
    $csvData = Import-Csv "column_d_results.csv"
    
    # Process each entry to split categories
    $categoryCount = @{}
    $totalCategories = 0
    $originalEntries = 0
    
    Write-Host "Processing and splitting categories..." -ForegroundColor Yellow
    
    foreach ($row in $csvData) {
        $value = $row.Value
        $count = [int]$row.Count
        $originalEntries += $count
        
        # Split the value by common separators
        $categories = @()
        
        if ($value -match '\+') {
            $categories = $value -split '\+' | ForEach-Object { $_.Trim() }
        } elseif ($value -match '、') {
            $categories = $value -split '、' | ForEach-Object { $_.Trim() }
        } elseif ($value -match '，') {
            $categories = $value -split '，' | ForEach-Object { $_.Trim() }
        } else {
            $categories = @($value.Trim())
        }
        
        # Count each category
        foreach ($category in $categories) {
            $cleanCategory = $category.Trim()
            if ($cleanCategory.Length -gt 1) {
                if ($categoryCount.ContainsKey($cleanCategory)) {
                    $categoryCount[$cleanCategory] += $count
                } else {
                    $categoryCount[$cleanCategory] = $count
                }
                $totalCategories += $count
            }
        }
    }
    
    Write-Host "Analysis completed!" -ForegroundColor Green
    Write-Host "Original entries: $originalEntries" -ForegroundColor Yellow
    Write-Host "Total category instances: $totalCategories" -ForegroundColor Yellow
    Write-Host "Unique categories: $($categoryCount.Count)" -ForegroundColor Yellow
    
    # Sort results
    $sortedResults = $categoryCount.GetEnumerator() | Sort-Object Value -Descending
    
    # Save to CSV
    $splitResults = @()
    $rank = 1
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        $splitResults += [PSCustomObject]@{
            Rank = $rank
            Category = $item.Key
            Count = $item.Value
            Percentage = $percentage
        }
        $rank++
    }
    
    $splitResults | Export-Csv -Path "split_categories_analysis.csv" -NoTypeInformation -Encoding UTF8
    
    # Save to text file
    $outputText = "D列拆分分类统计分析结果`n"
    $outputText += "========================================`n`n"
    $outputText += "原始条目数: $originalEntries`n"
    $outputText += "总分类实例数: $totalCategories`n"
    $outputText += "唯一分类数: $($categoryCount.Count)`n`n"
    $outputText += "详细统计:`n"
    $outputText += "------------------------------------------------------------`n"
    $outputText += "分类                           计数        占比(%)`n"
    $outputText += "------------------------------------------------------------`n"
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        $line = "{0,-30} {1,-10} {2,-10}" -f $item.Key, $item.Value, $percentage
        $outputText += "$line`n"
    }
    
    $outputText | Out-File -FilePath "split_categories_analysis.txt" -Encoding UTF8
    
    # Display results
    Write-Host "`n=== Split Categories Analysis Results ===" -ForegroundColor Cyan
    Write-Host "Original entries: $originalEntries" -ForegroundColor White
    Write-Host "Total category instances: $totalCategories" -ForegroundColor White
    Write-Host "Unique categories: $($categoryCount.Count)" -ForegroundColor White
    
    Write-Host "`nTOP 15 Photography Categories:" -ForegroundColor Cyan
    Write-Host "------------------------------------------------------------"
    Write-Host "Rank  Category                          Count    Percentage"
    Write-Host "------------------------------------------------------------"
    
    $top15 = $sortedResults | Select-Object -First 15
    $displayRank = 1
    foreach ($item in $top15) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        $line = "{0,-5} {1,-35} {2,-8} {3,-10}" -f $displayRank, $item.Key, $item.Value, "$percentage%"
        Write-Host $line -ForegroundColor White
        $displayRank++
    }
    
    Write-Host "`nResults saved to:" -ForegroundColor Green
    Write-Host "  - split_categories_analysis.csv" -ForegroundColor Yellow
    Write-Host "  - split_categories_analysis.txt" -ForegroundColor Yellow
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nSplit category analysis completed!" -ForegroundColor Green
