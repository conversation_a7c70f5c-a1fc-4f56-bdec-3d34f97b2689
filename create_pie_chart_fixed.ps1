# Fixed PowerShell script to create Excel with pie chart
Write-Host "Creating Excel file with pie chart..." -ForegroundColor Green

try {
    # Create Excel application
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    Write-Host "Excel application started" -ForegroundColor Yellow
    
    # Create workbook
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "Photography Statistics"
    Write-Host "Workbook created" -ForegroundColor Yellow
    
    # Add title
    $worksheet.Cells.Item(1, 1) = "Photography Category Analysis"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Add headers
    $worksheet.Cells.Item(3, 1) = "Category"
    $worksheet.Cells.Item(3, 2) = "Count"
    $worksheet.Cells.Item(3, 1).Font.Bold = $true
    $worksheet.Cells.Item(3, 2).Font.Bold = $true
    
    # Add data
    $worksheet.Cells.Item(4, 1) = "Portrait Photography"
    $worksheet.Cells.Item(4, 2) = 534
    $worksheet.Cells.Item(5, 1) = "Wedding Photography"
    $worksheet.Cells.Item(5, 2) = 145
    $worksheet.Cells.Item(6, 1) = "Others"
    $worksheet.Cells.Item(6, 2) = 136
    $worksheet.Cells.Item(7, 1) = "Commercial Photography"
    $worksheet.Cells.Item(7, 2) = 118
    $worksheet.Cells.Item(8, 1) = "Children Photography"
    $worksheet.Cells.Item(8, 2) = 117
    $worksheet.Cells.Item(9, 1) = "Landscape Photography"
    $worksheet.Cells.Item(9, 2) = 99
    $worksheet.Cells.Item(10, 1) = "Wedding Shooting"
    $worksheet.Cells.Item(10, 2) = 69
    $worksheet.Cells.Item(11, 1) = "Scenery & Humanities"
    $worksheet.Cells.Item(11, 2) = 62
    
    Write-Host "Data added" -ForegroundColor Yellow
    
    # Set column widths
    $worksheet.Columns("A:A").ColumnWidth = 20
    $worksheet.Columns("B:B").ColumnWidth = 10
    
    # Create chart
    $dataRange = $worksheet.Range("A3:B11")
    $chartObject = $worksheet.Shapes.AddChart()
    $chart = $chartObject.Chart
    
    Write-Host "Chart object created" -ForegroundColor Yellow
    
    # Configure chart
    $chart.ChartType = 5  # xlPie
    $chart.SetSourceData($dataRange)
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "Photography Category Distribution"
    
    # Position chart
    $chartObject.Left = 300
    $chartObject.Top = 50
    $chartObject.Width = 400
    $chartObject.Height = 300
    
    # Add data labels
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false
    
    Write-Host "Chart configured" -ForegroundColor Yellow
    
    # Save file with absolute path
    $currentDir = Get-Location
    $fileName = "Photography_Statistics_PieChart.xlsx"
    $fullPath = Join-Path $currentDir.Path $fileName
    
    Write-Host "Saving to: $fullPath" -ForegroundColor Yellow
    
    # Remove existing file if it exists
    if (Test-Path $fileName) {
        Remove-Item $fileName -Force
        Write-Host "Removed existing file" -ForegroundColor Gray
    }
    
    # Save workbook
    $workbook.SaveAs($fullPath)
    Write-Host "File saved successfully" -ForegroundColor Green
    
    # Close Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Excel closed" -ForegroundColor Yellow
    
    # Verify file exists
    if (Test-Path $fileName) {
        $fileInfo = Get-Item $fileName
        Write-Host "`n=== SUCCESS ===" -ForegroundColor Green
        Write-Host "File created: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "Size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor White
        Write-Host "Location: $($fileInfo.FullName)" -ForegroundColor White
        
        Write-Host "`nFile contains:" -ForegroundColor Cyan
        Write-Host "- Data table with photography categories" -ForegroundColor White
        Write-Host "- Pie chart with percentage labels" -ForegroundColor White
        Write-Host "- TOP 8 photography categories" -ForegroundColor White
        
        Write-Host "`nTOP 5 Categories:" -ForegroundColor Cyan
        Write-Host "1. Portrait Photography: 534 (26.9%)" -ForegroundColor White
        Write-Host "2. Wedding Photography: 145 (7.3%)" -ForegroundColor White
        Write-Host "3. Others: 136 (6.9%)" -ForegroundColor White
        Write-Host "4. Commercial Photography: 118 (5.9%)" -ForegroundColor White
        Write-Host "5. Children Photography: 117 (5.9%)" -ForegroundColor White
        
    } else {
        Write-Host "`nERROR: File was not created!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`nERROR occurred:" -ForegroundColor Red
    Write-Host "Message: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    # Cleanup
    try {
        if ($workbook) { 
            $workbook.Close($false)
            Write-Host "Workbook closed" -ForegroundColor Gray
        }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
            Write-Host "Excel application closed" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Cleanup completed" -ForegroundColor Gray
    }
}

Write-Host "`nScript completed!" -ForegroundColor Green
