#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D列数据统计分析并生成Excel饼图
专门处理 乱川线下活动数据表格20250726.xlsx 文件
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
import openpyxl.chart.label
from collections import Counter
import datetime
import os

def analyze_d_column(file_path):
    """
    分析Excel文件中D列的数据统计
    
    Args:
        file_path (str): Excel文件路径
    
    Returns:
        dict: 包含统计结果的字典
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return {}
        
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)
        worksheet = workbook.active
        
        print(f"✅ 成功读取Excel文件: {file_path}")
        print(f"📋 工作表名称: {worksheet.title}")
        print(f"📊 数据范围: {worksheet.max_row} 行 x {worksheet.max_column} 列")
        
        # 读取D列数据
        column_data = []
        max_row = worksheet.max_row
        
        print(f"🔍 开始读取D列数据...")
        
        for row in range(2, max_row + 1):  # 从第2行开始，跳过标题行
            cell_value = worksheet.cell(row=row, column=4).value  # D列是第4列
            if cell_value is not None and str(cell_value).strip():
                column_data.append(str(cell_value).strip())
        
        workbook.close()
        
        if not column_data:
            print("❌ D列没有有效数据")
            return {}
        
        print(f"📈 找到 {len(column_data)} 条有效数据")
        
        # 统计各个值的数量
        value_counts = Counter(column_data)
        total_count = len(column_data)
        
        # 创建统计结果
        results = {
            'total_count': total_count,
            'unique_values': len(value_counts),
            'statistics': []
        }
        
        print(f"\n=== D列统计结果 ===")
        print(f"总数据量: {total_count}")
        print(f"唯一值数量: {len(value_counts)}")
        print(f"\n详细统计:")
        print("-" * 60)
        print(f"{'值':<30} {'数量':<10} {'占比(%)':<10}")
        print("-" * 60)
        
        # 按数量排序
        for value, count in value_counts.most_common():
            percentage = round(count / total_count * 100, 2)
            print(f"{value:<30} {count:<10} {percentage:<10}")
            
            results['statistics'].append({
                'value': value,
                'count': count,
                'percentage': percentage
            })
        
        return results
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        return {}

def create_excel_with_pie_chart(results, output_file="D列统计分析饼图.xlsx"):
    """
    创建包含饼图的Excel文件
    
    Args:
        results (dict): 分析结果
        output_file (str): 输出文件名
    
    Returns:
        bool: 是否成功创建
    """
    try:
        print(f"\n📊 开始创建包含饼图的Excel文件...")
        
        # 创建工作簿
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "D列统计分析"
        
        print("📝 添加标题和数据...")
        
        # 设置样式
        title_font = Font(size=16, bold=True, color="2F4F4F")
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 添加标题
        worksheet['A1'] = "D列数据统计分析报告"
        worksheet['A1'].font = title_font
        worksheet.merge_cells('A1:C1')
        worksheet['A1'].alignment = Alignment(horizontal='center')
        
        # 添加元数据
        worksheet['A3'] = f"分析时间: {datetime.datetime.now().strftime('%Y年%m月%d日 %H:%M')}"
        worksheet['A4'] = f"总数据量: {results.get('total_count', 0)} 条"
        worksheet['A5'] = f"唯一值数量: {results.get('unique_values', 0)} 个"
        
        # 数据表格标题
        worksheet['A7'] = "类别"
        worksheet['B7'] = "数量"
        worksheet['C7'] = "占比(%)"
        
        # 设置表头样式
        for col in ['A7', 'B7', 'C7']:
            worksheet[col].font = header_font
            worksheet[col].fill = header_fill
            worksheet[col].border = border
            worksheet[col].alignment = Alignment(horizontal='center')
        
        # 添加数据
        statistics = results.get('statistics', [])
        for i, stat in enumerate(statistics, start=8):
            worksheet[f'A{i}'] = str(stat.get('value', ''))
            worksheet[f'B{i}'] = stat.get('count', 0)
            worksheet[f'C{i}'] = stat.get('percentage', 0)
            
            # 设置边框和对齐
            worksheet[f'A{i}'].border = border
            worksheet[f'B{i}'].border = border
            worksheet[f'C{i}'].border = border
            worksheet[f'B{i}'].alignment = Alignment(horizontal='center')
            worksheet[f'C{i}'].alignment = Alignment(horizontal='center')
        
        print("📊 创建饼图...")
        
        # 创建饼图
        pie_chart = PieChart()
        pie_chart.title = "D列数据分布统计"
        pie_chart.height = 10  # 图表高度
        pie_chart.width = 15   # 图表宽度
        
        # 设置数据范围
        data_end_row = 7 + len(statistics)
        labels = Reference(worksheet, min_col=1, min_row=8, max_row=data_end_row)
        data_ref = Reference(worksheet, min_col=2, min_row=8, max_row=data_end_row)
        
        pie_chart.add_data(data_ref)
        pie_chart.set_categories(labels)
        
        # 设置数据标签显示百分比
        pie_chart.dataLabels = openpyxl.chart.label.DataLabelList()
        pie_chart.dataLabels.showPercent = True
        pie_chart.dataLabels.showVal = False
        pie_chart.dataLabels.showCatName = True
        
        # 设置图例
        pie_chart.legend.position = 'r'  # 右侧
        
        # 将图表添加到工作表
        worksheet.add_chart(pie_chart, "E7")
        
        print("🎨 设置格式...")
        
        # 调整列宽
        worksheet.column_dimensions['A'].width = 25
        worksheet.column_dimensions['B'].width = 12
        worksheet.column_dimensions['C'].width = 12
        
        # 添加统计摘要
        summary_start_row = data_end_row + 3
        worksheet[f'A{summary_start_row}'] = "📊 统计摘要"
        worksheet[f'A{summary_start_row}'].font = Font(bold=True, size=12, color="2F4F4F")
        
        if statistics:
            top_item = statistics[0]
            worksheet[f'A{summary_start_row + 1}'] = f"• 最多的类别: {top_item['value']} ({top_item['percentage']}%)"
            worksheet[f'A{summary_start_row + 2}'] = f"• 数据总量: {results['total_count']} 条"
            worksheet[f'A{summary_start_row + 3}'] = f"• 类别总数: {results['unique_values']} 个"
        
        print("💾 保存Excel文件...")
        
        # 保存文件
        workbook.save(output_file)
        workbook.close()
        
        print(f"✅ 成功创建Excel文件: {output_file}")
        print(f"📈 包含内容:")
        print(f"   - 数据表格 ({len(statistics)} 个类别)")
        print(f"   - 饼图 (带百分比和类别标签)")
        print(f"   - 统计摘要")
        
        if statistics:
            print(f"\n🏆 TOP 5 类别:")
            for i, stat in enumerate(statistics[:5], 1):
                print(f"   {i}. {stat['value']}: {stat['count']} 次 ({stat['percentage']}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建Excel文件时出现错误: {str(e)}")
        return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("🎯 D列数据统计分析并生成Excel饼图")
    print("=" * 60)
    
    # Excel文件路径
    file_path = "乱川线下活动数据表格20250726.xlsx"
    
    print(f"📁 目标文件: {file_path}")
    
    # 分析D列数据
    results = analyze_d_column(file_path)
    
    if results:
        print("\n✅ 分析完成！")
        
        # 创建包含饼图的Excel文件
        output_file = "D列统计分析饼图.xlsx"
        success = create_excel_with_pie_chart(results, output_file)
        
        if success:
            print(f"\n🎉 任务完成!")
            print(f"📁 请查看生成的文件: {output_file}")
        else:
            print(f"\n❌ 生成Excel文件失败!")
    else:
        print("\n❌ 分析失败，请检查文件路径和数据格式")

if __name__ == "__main__":
    main()
