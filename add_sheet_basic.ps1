# Basic PowerShell script to add analysis sheet
param(
    [string]$ExcelFile = ""
)

# Find Excel file
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $originalSheet = $workbook.ActiveSheet
    
    # Read data from column D
    $lastRow = $originalSheet.UsedRange.Rows.Count
    $columnData = @()
    
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        }
    }
    
    # Count values
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    
    Write-Host "Data analysis completed. Total records: $totalCount" -ForegroundColor Yellow
    
    # Create new sheet
    $newSheet = $workbook.Worksheets.Add()
    $newSheet.Name = "Analysis"
    
    Write-Host "Created new sheet: Analysis" -ForegroundColor Green
    
    # Add title
    $newSheet.Cells.Item(1, 1).Value2 = "Photography Type Analysis"
    $newSheet.Cells.Item(1, 1).Font.Size = 16
    $newSheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Add summary
    $newSheet.Cells.Item(3, 1).Value2 = "Total Records:"
    $newSheet.Cells.Item(3, 2).Value2 = $totalCount
    $newSheet.Cells.Item(4, 1).Value2 = "Unique Types:"
    $newSheet.Cells.Item(4, 2).Value2 = $valueCount.Count
    
    # Add table headers
    $newSheet.Cells.Item(6, 1).Value2 = "Rank"
    $newSheet.Cells.Item(6, 2).Value2 = "Photography Type"
    $newSheet.Cells.Item(6, 3).Value2 = "Count"
    $newSheet.Cells.Item(6, 4).Value2 = "Percentage"
    
    # Format headers
    $headerRange = $newSheet.Range("A6:D6")
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099
    
    # Add data
    $row = 7
    $rank = 1
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($row, 1).Value2 = $rank
        $newSheet.Cells.Item($row, 2).Value2 = $item.Key
        $newSheet.Cells.Item($row, 3).Value2 = $item.Value
        $newSheet.Cells.Item($row, 4).Value2 = $percentage
        
        $row++
        $rank++
    }
    
    # Auto-fit columns
    $newSheet.Columns("A:D").AutoFit()
    $newSheet.Columns("B").ColumnWidth = 40
    
    # Save and close
    $workbook.Save()
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Successfully added analysis sheet!" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}
