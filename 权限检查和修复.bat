@echo off
chcp 65001 >nul
echo ========================================
echo 权限检查和修复工具
echo ========================================
echo.

echo [1/4] 检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ 当前具有管理员权限
) else (
    echo ✗ 当前没有管理员权限
    echo.
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo 或者按任意键以普通用户权限继续...
    pause >nul
)

echo.
echo [2/4] 检查PowerShell执行策略...
powershell -Command "Get-ExecutionPolicy" >temp_policy.txt 2>&1
set /p current_policy=<temp_policy.txt
del temp_policy.txt >nul 2>&1
echo 当前执行策略: %current_policy%

if /i "%current_policy%"=="Restricted" (
    echo ✗ 执行策略过于严格，需要修改
    echo.
    echo 正在尝试修改执行策略...
    powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force" >nul 2>&1
    if %errorLevel% == 0 (
        echo ✓ 执行策略已修改为 RemoteSigned
    ) else (
        echo ✗ 无法修改执行策略，可能需要管理员权限
    )
) else (
    echo ✓ 执行策略允许脚本运行
)

echo.
echo [3/4] 检查Excel COM组件...
powershell -Command "try { $excel = New-Object -ComObject Excel.Application; $excel.Quit(); Write-Host '✓ Excel COM组件可用' } catch { Write-Host '✗ Excel COM组件不可用: ' + $_.Exception.Message }" 2>&1

echo.
echo [4/4] 尝试创建测试Excel文件...
powershell -ExecutionPolicy Bypass -Command "try { $excel = New-Object -ComObject Excel.Application; $excel.Visible = $false; $wb = $excel.Workbooks.Add(); $ws = $wb.ActiveSheet; $ws.Cells.Item(1,1) = 'Test'; $wb.SaveAs((Get-Location).Path + '\权限测试.xlsx'); $wb.Close(); $excel.Quit(); Write-Host '✓ 测试文件创建成功' } catch { Write-Host '✗ 测试文件创建失败: ' + $_.Exception.Message }" 2>&1

echo.
echo ========================================
echo 权限检查完成
echo ========================================
echo.

if exist "权限测试.xlsx" (
    echo ✓ 权限检查通过！现在可以生成饼图了。
    del "权限测试.xlsx" >nul 2>&1
    echo.
    echo 是否立即生成摄影分类饼图？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo.
        echo 正在生成饼图...
        powershell -ExecutionPolicy Bypass -File create_pie_chart_fixed.ps1
    )
) else (
    echo ✗ 权限检查未通过，请尝试以下解决方案：
    echo.
    echo 1. 以管理员身份重新运行此文件
    echo 2. 手动修改PowerShell执行策略：
    echo    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
    echo 3. 确保已安装Microsoft Excel
    echo 4. 检查防病毒软件是否阻止了脚本执行
)

echo.
pause
