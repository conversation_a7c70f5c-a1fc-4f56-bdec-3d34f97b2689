' VBScript to create Excel file with pie chart
Dim excel, workbook, worksheet, chart, chartObject, dataRange
Dim outputFile

' Create Excel application
Set excel = CreateObject("Excel.Application")
excel.Visible = False
excel.DisplayAlerts = False

' Create workbook
Set workbook = excel.Workbooks.Add()
Set worksheet = workbook.ActiveSheet
worksheet.Name = "摄影分类统计"

' Add title
worksheet.Cells(1, 1).Value = "摄影分类统计分析（拆分版）"
worksheet.Cells(1, 1).Font.Size = 16
worksheet.Cells(1, 1).Font.Bold = True

' Add metadata
worksheet.Cells(2, 1).Value = "分析时间: 2025年7月26日"
worksheet.Cells(3, 1).Value = "数据说明: 多分类条目已拆分统计"

' Add headers
worksheet.Cells(5, 1).Value = "摄影分类"
worksheet.Cells(5, 2).Value = "计数"
worksheet.Cells(5, 1).Font.Bold = True
worksheet.Cells(5, 2).Font.Bold = True

' Add data
worksheet.Cells(6, 1).Value = "人像摄影"
worksheet.Cells(6, 2).Value = 534
worksheet.Cells(7, 1).Value = "婚礼摄影"
worksheet.Cells(7, 2).Value = 145
worksheet.Cells(8, 1).Value = "其他"
worksheet.Cells(8, 2).Value = 136
worksheet.Cells(9, 1).Value = "商业摄影"
worksheet.Cells(9, 2).Value = 118
worksheet.Cells(10, 1).Value = "儿童摄影"
worksheet.Cells(10, 2).Value = 117
worksheet.Cells(11, 1).Value = "风景摄影"
worksheet.Cells(11, 2).Value = 99
worksheet.Cells(12, 1).Value = "婚礼跟拍"
worksheet.Cells(12, 2).Value = 69
worksheet.Cells(13, 1).Value = "风光人文"
worksheet.Cells(13, 2).Value = 62
worksheet.Cells(14, 1).Value = "视频制作"
worksheet.Cells(14, 2).Value = 52
worksheet.Cells(15, 1).Value = "儿童亲子"
worksheet.Cells(15, 2).Value = 40

' Set column widths
worksheet.Columns("A:A").ColumnWidth = 15
worksheet.Columns("B:B").ColumnWidth = 8

' Create chart
Set dataRange = worksheet.Range("A5:B15")
Set chartObject = worksheet.Shapes.AddChart()
Set chart = chartObject.Chart

' Configure chart
chart.ChartType = 5  ' xlPie
chart.SetSourceData dataRange
chart.HasTitle = True
chart.ChartTitle.Text = "摄影分类分布统计"

' Position chart
chartObject.Left = 200
chartObject.Top = 100
chartObject.Width = 450
chartObject.Height = 350

' Add data labels
chart.SeriesCollection(1).HasDataLabels = True
chart.SeriesCollection(1).DataLabels.ShowPercentage = True
chart.SeriesCollection(1).DataLabels.ShowValue = False
chart.SeriesCollection(1).DataLabels.ShowCategoryName = False

' Add summary
worksheet.Cells(17, 1).Value = "统计摘要:"
worksheet.Cells(17, 1).Font.Bold = True
worksheet.Cells(18, 1).Value = "• 人像摄影占主导地位 (26.9%)"
worksheet.Cells(19, 1).Value = "• 前5类占总数的53.9%"
worksheet.Cells(20, 1).Value = "• 显示多元化发展趋势"

' Save file
outputFile = "摄影分类统计分析_含饼图.xlsx"
workbook.SaveAs CreateObject("Scripting.FileSystemObject").GetAbsolutePathName(outputFile)

' Close
workbook.Close False
excel.Quit

' Clean up
Set chart = Nothing
Set chartObject = Nothing
Set dataRange = Nothing
Set worksheet = Nothing
Set workbook = Nothing
Set excel = Nothing

' Display result
WScript.Echo "Excel文件已创建: " & outputFile
WScript.Echo "包含数据表格和饼图"
