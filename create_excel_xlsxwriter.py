#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用xlsxwriter创建包含饼图的Excel文件
"""

import sys
import os

def create_excel_with_pie_chart():
    """使用xlsxwriter创建包含饼图的Excel文件"""
    
    try:
        import xlsxwriter
        print("✅ xlsxwriter库可用")
    except ImportError:
        print("❌ xlsxwriter库未安装")
        print("💡 请安装: pip install xlsxwriter")
        return False
    
    try:
        print("📊 开始创建包含饼图的Excel文件...")
        
        # 创建工作簿
        output_file = "摄影分类统计分析_含饼图.xlsx"
        workbook = xlsxwriter.Workbook(output_file)
        worksheet = workbook.add_worksheet('摄影分类统计')
        
        print("📝 添加格式和数据...")
        
        # 定义格式
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'left'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#ADD8E6',
            'border': 1,
            'align': 'center'
        })
        
        data_format = workbook.add_format({
            'border': 1,
            'align': 'center'
        })
        
        # 添加标题
        worksheet.write('A1', '摄影分类统计分析（拆分版）', title_format)
        
        # 添加说明
        worksheet.write('A3', '分析时间: 2025年7月26日')
        worksheet.write('A4', '数据说明: 多分类条目已拆分统计')
        worksheet.write('A5', '总分类数: 1986个实例，唯一分类: 26种')
        
        # 添加表格标题
        worksheet.write('A7', '摄影分类', header_format)
        worksheet.write('B7', '计数', header_format)
        worksheet.write('C7', '占比(%)', header_format)
        
        # 数据
        data = [
            ['人像摄影', 534, 26.89],
            ['婚礼摄影', 145, 7.30],
            ['其他', 136, 6.85],
            ['商业摄影', 118, 5.94],
            ['儿童摄影', 117, 5.89],
            ['风景摄影', 99, 4.99],
            ['婚礼跟拍', 69, 3.47],
            ['风光人文', 62, 3.12],
            ['视频制作', 52, 2.62],
            ['儿童亲子', 40, 2.01],
            ['未分类', 37, 1.86],
            ['自媒体VLOG', 28, 1.41]
        ]
        
        # 添加数据
        for i, (category, count, percentage) in enumerate(data, start=8):
            worksheet.write(f'A{i}', category, data_format)
            worksheet.write(f'B{i}', count, data_format)
            worksheet.write(f'C{i}', percentage, data_format)
        
        print("📊 创建饼图...")
        
        # 创建饼图
        chart = workbook.add_chart({'type': 'pie'})
        
        # 添加数据系列
        chart.add_series({
            'name': '摄影分类分布',
            'categories': ['摄影分类统计', 7, 0, 19, 0],  # A8:A19
            'values': ['摄影分类统计', 7, 1, 19, 1],      # B8:B19
            'data_labels': {
                'percentage': True,
                'value': False,
                'category': False,
                'font': {'size': 10}
            }
        })
        
        # 设置图表标题
        chart.set_title({
            'name': '摄影分类分布统计',
            'name_font': {'size': 14, 'bold': True}
        })
        
        # 设置图例
        chart.set_legend({
            'position': 'right',
            'font': {'size': 9}
        })
        
        # 设置图表样式
        chart.set_style(2)
        
        # 插入图表到工作表
        worksheet.insert_chart('E7', chart, {
            'x_scale': 1.2,
            'y_scale': 1.2
        })
        
        print("🎨 设置格式...")
        
        # 设置列宽
        worksheet.set_column('A:A', 18)
        worksheet.set_column('B:B', 8)
        worksheet.set_column('C:C', 10)
        
        # 添加统计摘要
        summary_row = len(data) + 10
        summary_format = workbook.add_format({'bold': True, 'font_size': 12})
        
        worksheet.write(f'A{summary_row}', '统计摘要:', summary_format)
        worksheet.write(f'A{summary_row + 1}', '• 人像摄影占主导地位 (26.9%)')
        worksheet.write(f'A{summary_row + 2}', '• 前5类占总数的53.9%')
        worksheet.write(f'A{summary_row + 3}', '• 显示多元化发展趋势')
        worksheet.write(f'A{summary_row + 4}', '• 复合型摄影师比例较高')
        
        print("💾 保存Excel文件...")
        
        # 关闭工作簿
        workbook.close()
        
        print(f"✅ 成功创建Excel文件: {output_file}")
        
        # 验证文件
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📁 文件大小: {file_size / 1024:.2f} KB")
            
            print(f"\n📈 文件内容:")
            print(f"   ✓ 数据表格 (TOP 12分类)")
            print(f"   ✓ 饼图 (带百分比标签)")
            print(f"   ✓ 图例和标题")
            print(f"   ✓ 统计摘要")
            
            print(f"\n🏆 TOP 5 分类:")
            for i, (category, count, percentage) in enumerate(data[:5], 1):
                print(f"   {i}. {category}: {count}次 ({percentage}%)")
            
            return True
        else:
            print("❌ 文件创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 创建Excel文件时出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 创建摄影分类统计Excel文件（含饼图） ===")
    print("使用xlsxwriter库生成")
    
    success = create_excel_with_pie_chart()
    
    if success:
        print("\n🎉 任务完成!")
        print("📁 您现在可以打开Excel文件查看饼图和统计数据")
        print("💡 文件包含完整的数据表格和可视化饼图")
    else:
        print("\n💥 任务失败!")
        print("💡 建议:")
        print("   1. 安装xlsxwriter: pip install xlsxwriter")
        print("   2. 确保有写入权限")
        print("   3. 检查磁盘空间")

if __name__ == "__main__":
    main()
