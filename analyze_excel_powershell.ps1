# PowerShell脚本：分析Excel文件D列数据
# 统计D列中各个值的数量和占比

param(
    [string]$ExcelFile = "乱川线下活动数据表格【成都场】20250726.xlsx",
    [int]$ColumnIndex = 4  # D列是第4列
)

# 检查文件是否存在
if (-not (Test-Path $ExcelFile)) {
    Write-Host "错误：找不到Excel文件 $ExcelFile" -ForegroundColor Red
    exit 1
}

try {
    # 创建Excel应用程序对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "正在打开Excel文件: $ExcelFile" -ForegroundColor Green
    
    # 打开工作簿
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $worksheet = $workbook.ActiveSheet
    
    Write-Host "工作表名称: $($worksheet.Name)" -ForegroundColor Yellow
    
    # 获取使用的行数
    $lastRow = $worksheet.UsedRange.Rows.Count
    Write-Host "数据行数: $lastRow" -ForegroundColor Yellow
    
    # 读取D列数据（从第2行开始，跳过标题）
    $columnData = @()
    $emptyCount = 0
    
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $worksheet.Cells.Item($row, $ColumnIndex).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        } else {
            $emptyCount++
        }
    }
    
    # 关闭Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    if ($columnData.Count -eq 0) {
        Write-Host "D列没有有效数据" -ForegroundColor Red
        exit 1
    }
    
    # 统计各个值的数量
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    
    # 显示结果
    Write-Host "`n=== D列统计结果 ===" -ForegroundColor Cyan
    Write-Host "总数据量: $totalCount" -ForegroundColor Green
    Write-Host "唯一值数量: $($valueCount.Count)" -ForegroundColor Green
    Write-Host "空值数量: $emptyCount" -ForegroundColor Yellow
    
    Write-Host "`n详细统计:" -ForegroundColor Cyan
    Write-Host ("-" * 60) -ForegroundColor Gray
    Write-Host ("{0,-25} {1,-10} {2,-10}" -f "值", "数量", "占比(%)") -ForegroundColor White
    Write-Host ("-" * 60) -ForegroundColor Gray
    
    # 按数量排序并显示
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    $results = @()
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        Write-Host ("{0,-25} {1,-10} {2,-10}" -f $item.Key, $item.Value, $percentage) -ForegroundColor White
        
        $results += [PSCustomObject]@{
            Value = $item.Key
            Count = $item.Value
            Percentage = $percentage
        }
    }
    
    # 保存结果到CSV文件
    $results | Export-Csv -Path "column_d_analysis_results.csv" -NoTypeInformation -Encoding UTF8
    Write-Host "`n结果已保存到: column_d_analysis_results.csv" -ForegroundColor Green
    
    # 保存结果到文本文件
    $outputText = "D列统计分析结果`n"
    $outputText += "==============================`n`n"
    $outputText += "总数据量: $totalCount`n"
    $outputText += "唯一值数量: $($valueCount.Count)`n"
    $outputText += "空值数量: $emptyCount`n`n"
    $outputText += "详细统计:`n"
    $outputText += "------------------------------------------------------------`n"
    $outputText += "值                        数量        占比(%)`n"
    $outputText += "------------------------------------------------------------`n"

    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        $line = "{0,-25} {1,-10} {2,-10}" -f $item.Key, $item.Value, $percentage
        $outputText += "$line`n"
    }
    
    $outputText | Out-File -FilePath "column_d_analysis_results.txt" -Encoding UTF8
    Write-Host "结果已保存到: column_d_analysis_results.txt" -ForegroundColor Green
    
} catch {
    Write-Host "分析过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    
    # 确保Excel进程被关闭
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {
        # 忽略清理错误
    }
    
    exit 1
}

Write-Host "`n分析完成！" -ForegroundColor Green
