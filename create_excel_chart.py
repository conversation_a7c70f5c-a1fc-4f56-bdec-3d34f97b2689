#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建包含饼图的Excel文件
"""

try:
    import openpyxl
    from openpyxl.chart import PieChart, Reference
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("openpyxl未安装，请先安装: pip install openpyxl")

def create_excel_with_pie_chart():
    """创建包含饼图的Excel文件"""
    
    if not OPENPYXL_AVAILABLE:
        print("❌ 无法创建Excel文件，缺少openpyxl库")
        return False
    
    try:
        print("📊 开始创建包含饼图的Excel文件...")
        
        # 创建工作簿
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "摄影分类统计"
        
        print("📝 添加标题和数据...")
        
        # 设置样式
        title_font = Font(size=16, bold=True)
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 添加标题
        worksheet['A1'] = "摄影分类统计分析（拆分版）"
        worksheet['A1'].font = title_font
        
        # 添加元数据
        worksheet['A3'] = "分析时间: 2025年7月26日"
        worksheet['A4'] = "总分类数: 1986个实例"
        worksheet['A5'] = "数据说明: 多分类条目已拆分统计"
        
        # 数据表格标题
        worksheet['A7'] = "摄影分类"
        worksheet['B7'] = "计数"
        worksheet['C7'] = "占比(%)"
        
        # 设置表头样式
        for col in ['A7', 'B7', 'C7']:
            worksheet[col].font = header_font
            worksheet[col].fill = header_fill
            worksheet[col].border = border
            worksheet[col].alignment = Alignment(horizontal='center')
        
        # 数据（TOP 12用于饼图）
        data = [
            ("人像摄影", 534, 26.89),
            ("婚礼摄影", 145, 7.30),
            ("其他", 136, 6.85),
            ("商业摄影", 118, 5.94),
            ("儿童摄影", 117, 5.89),
            ("风景摄影", 99, 4.99),
            ("婚礼跟拍", 69, 3.47),
            ("风光人文", 62, 3.12),
            ("视频制作", 52, 2.62),
            ("儿童亲子", 40, 2.01),
            ("未分类", 37, 1.86),
            ("自媒体VLOG", 28, 1.41)
        ]
        
        # 添加数据
        for i, (category, count, percentage) in enumerate(data, start=8):
            worksheet[f'A{i}'] = category
            worksheet[f'B{i}'] = count
            worksheet[f'C{i}'] = percentage
            
            # 设置边框
            for col in ['A', 'B', 'C']:
                worksheet[f'{col}{i}'].border = border
        
        print("📊 创建饼图...")
        
        # 创建饼图
        pie_chart = PieChart()
        pie_chart.title = "摄影分类分布统计"
        pie_chart.title.tx.rich.p[0].r.rPr.sz = 1400  # 字体大小
        
        # 设置数据范围
        labels = Reference(worksheet, min_col=1, min_row=8, max_row=19)  # A8:A19
        data_ref = Reference(worksheet, min_col=2, min_row=8, max_row=19)  # B8:B19
        
        pie_chart.add_data(data_ref)
        pie_chart.set_categories(labels)
        
        # 设置数据标签显示百分比
        pie_chart.dataLabels = openpyxl.chart.label.DataLabelList()
        pie_chart.dataLabels.showPercent = True
        pie_chart.dataLabels.showVal = False
        pie_chart.dataLabels.showCatName = False
        
        # 设置图例
        pie_chart.legend.position = 'r'  # 右侧
        
        # 将图表添加到工作表
        worksheet.add_chart(pie_chart, "E7")
        
        print("🎨 设置格式...")
        
        # 调整列宽
        worksheet.column_dimensions['A'].width = 18
        worksheet.column_dimensions['B'].width = 8
        worksheet.column_dimensions['C'].width = 10
        
        # 添加统计摘要
        summary_row = len(data) + 10
        worksheet[f'A{summary_row}'] = "统计摘要:"
        worksheet[f'A{summary_row}'].font = Font(bold=True, size=12)
        
        worksheet[f'A{summary_row + 1}'] = "• 人像摄影占主导地位 (26.9%)"
        worksheet[f'A{summary_row + 2}'] = "• 前5类占总数的53.9%"
        worksheet[f'A{summary_row + 3}'] = "• 显示多元化发展趋势"
        worksheet[f'A{summary_row + 4}'] = "• 复合型摄影师比例较高"
        
        print("💾 保存Excel文件...")
        
        # 保存文件
        output_file = "摄影分类统计饼图.xlsx"
        workbook.save(output_file)
        workbook.close()
        
        print(f"✅ 成功创建Excel文件: {output_file}")
        print(f"📈 包含内容:")
        print(f"   - 数据表格 (TOP 12分类)")
        print(f"   - 饼图 (带百分比标签)")
        print(f"   - 统计摘要")
        
        print(f"\n🏆 TOP 5 分类:")
        for i, (category, count, percentage) in enumerate(data[:5], 1):
            print(f"   {i}. {category}: {count}次 ({percentage}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建Excel文件时出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 创建摄影分类统计Excel文件（含饼图） ===")
    
    success = create_excel_with_pie_chart()
    
    if success:
        print("\n🎉 任务完成!")
        print("📁 您可以打开Excel文件查看饼图和统计数据")
    else:
        print("\n💥 任务失败!")
        print("💡 建议:")
        print("   1. 安装openpyxl: pip install openpyxl")
        print("   2. 或者手动创建Excel文件并导入CSV数据")

if __name__ == "__main__":
    main()
