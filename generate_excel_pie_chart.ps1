# Generate Excel file with pie chart for photography categories
Write-Host "=== 生成包含饼图的Excel文件 ===" -ForegroundColor <PERSON>an

try {
    Write-Host "启动Excel应用程序..." -ForegroundColor Yellow
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "创建工作簿..." -ForegroundColor Yellow
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "摄影分类统计"
    
    Write-Host "添加标题和数据..." -ForegroundColor Yellow
    
    # 添加标题
    $worksheet.Cells.Item(1, 1) = "摄影分类统计分析（拆分版）"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # 添加说明
    $worksheet.Cells.Item(2, 1) = "分析时间: $(Get-Date -Format 'yyyy年MM月dd日')"
    $worksheet.Cells.Item(3, 1) = "数据说明: 多分类条目已拆分统计"
    $worksheet.Cells.Item(4, 1) = "总分类数: 1986个实例，唯一分类: 26种"
    
    # 添加表格标题
    $worksheet.Cells.Item(6, 1) = "摄影分类"
    $worksheet.Cells.Item(6, 2) = "计数"
    $worksheet.Cells.Item(6, 3) = "占比(%)"
    
    # 设置表头格式
    $headerRange = $worksheet.Range("A6:C6")
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # 浅蓝色
    
    # 数据数组 (TOP 12)
    $data = @(
        @("人像摄影", 534, 26.89),
        @("婚礼摄影", 145, 7.30),
        @("其他", 136, 6.85),
        @("商业摄影", 118, 5.94),
        @("儿童摄影", 117, 5.89),
        @("风景摄影", 99, 4.99),
        @("婚礼跟拍", 69, 3.47),
        @("风光人文", 62, 3.12),
        @("视频制作", 52, 2.62),
        @("儿童亲子", 40, 2.01),
        @("未分类", 37, 1.86),
        @("自媒体VLOG", 28, 1.41)
    )
    
    # 添加数据到工作表
    $row = 7
    foreach ($item in $data) {
        $worksheet.Cells.Item($row, 1) = $item[0]
        $worksheet.Cells.Item($row, 2) = $item[1]
        $worksheet.Cells.Item($row, 3) = $item[2]
        $row++
    }
    
    Write-Host "设置列宽..." -ForegroundColor Yellow
    $worksheet.Columns("A:A").ColumnWidth = 18
    $worksheet.Columns("B:B").ColumnWidth = 8
    $worksheet.Columns("C:C").ColumnWidth = 10
    
    Write-Host "创建饼图..." -ForegroundColor Yellow
    
    # 选择数据范围用于图表 (分类名称和计数)
    $chartRange = $worksheet.Range("A6:B18")  # 包括标题行
    
    # 创建图表
    $chartObject = $worksheet.Shapes.AddChart2(201, 5)  # xlPie = 5
    $chart = $chartObject.Chart
    
    # 设置数据源
    $chart.SetSourceData($chartRange)
    
    # 设置图表标题
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "摄影分类分布统计"
    $chart.ChartTitle.Font.Size = 14
    $chart.ChartTitle.Font.Bold = $true
    
    # 设置图表位置和大小
    $chartObject.Left = $worksheet.Cells.Item(6, 5).Left  # E列开始
    $chartObject.Top = $worksheet.Cells.Item(6, 5).Top
    $chartObject.Width = 450
    $chartObject.Height = 350
    
    Write-Host "设置图表格式..." -ForegroundColor Yellow
    
    # 设置图例
    $chart.HasLegend = $true
    $chart.Legend.Position = -4152  # xlLegendPositionRight
    
    # 添加数据标签显示百分比
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false
    $chart.SeriesCollection(1).DataLabels.ShowCategoryName = $false
    $chart.SeriesCollection(1).DataLabels.Font.Size = 10
    
    # 设置图表样式
    $chart.ChartStyle = 2  # 使用预定义样式
    
    Write-Host "添加统计摘要..." -ForegroundColor Yellow
    
    # 在数据下方添加摘要
    $summaryRow = $row + 2
    $worksheet.Cells.Item($summaryRow, 1) = "统计摘要:"
    $worksheet.Cells.Item($summaryRow, 1).Font.Bold = $true
    $worksheet.Cells.Item($summaryRow, 1).Font.Size = 12
    
    $worksheet.Cells.Item($summaryRow + 1, 1) = "• 人像摄影占主导地位 (26.9%)"
    $worksheet.Cells.Item($summaryRow + 2, 1) = "• 前5类占总数的53.9%"
    $worksheet.Cells.Item($summaryRow + 3, 1) = "• 显示多元化发展趋势"
    $worksheet.Cells.Item($summaryRow + 4, 1) = "• 复合型摄影师比例较高"
    
    Write-Host "保存Excel文件..." -ForegroundColor Yellow
    
    # 保存文件
    $outputFile = "摄影分类统计分析_含饼图.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    
    # 删除已存在的文件
    if (Test-Path $outputFile) {
        Remove-Item $outputFile -Force
    }
    
    $workbook.SaveAs($fullPath)
    
    Write-Host "关闭Excel..." -ForegroundColor Yellow
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "`n=== 成功! ===" -ForegroundColor Green
    Write-Host "Excel文件已创建: $outputFile" -ForegroundColor Yellow
    
    # 验证文件是否存在
    if (Test-Path $outputFile) {
        $fileInfo = Get-Item $outputFile
        Write-Host "`n文件信息:" -ForegroundColor Cyan
        Write-Host "文件名: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor White
        Write-Host "创建时间: $($fileInfo.CreationTime)" -ForegroundColor White
        
        Write-Host "`n文件内容:" -ForegroundColor Cyan
        Write-Host "✓ 数据表格 (TOP 12分类)" -ForegroundColor White
        Write-Host "✓ 饼图 (带百分比标签)" -ForegroundColor White
        Write-Host "✓ 图例和标题" -ForegroundColor White
        Write-Host "✓ 统计摘要" -ForegroundColor White
        
        Write-Host "`nTOP 5 分类:" -ForegroundColor Cyan
        Write-Host "1. 人像摄影: 534次 (26.89%)" -ForegroundColor White
        Write-Host "2. 婚礼摄影: 145次 (7.30%)" -ForegroundColor White
        Write-Host "3. 其他: 136次 (6.85%)" -ForegroundColor White
        Write-Host "4. 商业摄影: 118次 (5.94%)" -ForegroundColor White
        Write-Host "5. 儿童摄影: 117次 (5.89%)" -ForegroundColor White
        
    } else {
        Write-Host "警告: 文件创建后未找到!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`n错误发生:" -ForegroundColor Red
    Write-Host "消息: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "行号: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    # 清理资源
    try {
        if ($workbook) { 
            $workbook.Close($false)
            Write-Host "工作簿已关闭" -ForegroundColor Gray
        }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
            Write-Host "Excel应用程序已关闭" -ForegroundColor Gray
        }
    } catch {
        Write-Host "清理完成" -ForegroundColor Gray
    }
    
    exit 1
}

Write-Host "`n任务完成! 您现在可以打开Excel文件查看饼图。" -ForegroundColor Green
