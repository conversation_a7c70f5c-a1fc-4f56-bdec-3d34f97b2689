# PowerShell脚本：在原Excel文件中新建统计分析sheet
param(
    [string]$ExcelFile = ""
)

# Find Excel file if not specified
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found in current directory" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
    Write-Host "Using Excel file: $ExcelFile" -ForegroundColor Yellow
}

if (-not (Test-Path $ExcelFile)) {
    Write-Host "Error: Excel file not found: $ExcelFile" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    # 创建Excel应用程序对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # 打开工作簿
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    
    # 首先读取原始数据进行分析
    $originalSheet = $workbook.ActiveSheet
    Write-Host "Reading data from original sheet: $($originalSheet.Name)" -ForegroundColor Yellow
    
    $lastRow = $originalSheet.UsedRange.Rows.Count
    
    # 读取D列数据（从第2行开始，跳过标题）
    $columnData = @()
    $emptyCount = 0
    
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        } else {
            $emptyCount++
        }
    }
    
    if ($columnData.Count -eq 0) {
        Write-Host "No valid data in column D" -ForegroundColor Red
        $workbook.Close($false)
        $excel.Quit()
        exit 1
    }
    
    # 统计各个值的数量
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    
    Write-Host "Analysis completed. Creating new sheet..." -ForegroundColor Green
    
    # 检查是否已存在"D列统计分析"工作表，如果存在则删除
    $sheetName = "D列统计分析"
    $existingSheet = $null
    foreach ($sheet in $workbook.Worksheets) {
        if ($sheet.Name -eq $sheetName) {
            $existingSheet = $sheet
            break
        }
    }
    
    if ($existingSheet -ne $null) {
        Write-Host "Deleting existing analysis sheet..." -ForegroundColor Yellow
        $existingSheet.Delete()
    }
    
    # 创建新的工作表
    $newSheet = $workbook.Worksheets.Add()
    $newSheet.Name = $sheetName
    
    Write-Host "Created new sheet: $sheetName" -ForegroundColor Green
    
    # 设置标题和基本信息
    $newSheet.Cells.Item(1, 1).Value2 = "D列摄影类型统计分析报告"
    $newSheet.Cells.Item(1, 1).Font.Size = 16
    $newSheet.Cells.Item(1, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(3, 1).Value2 = "分析时间："
    $newSheet.Cells.Item(3, 2).Value2 = (Get-Date).ToString("yyyy年MM月dd日 HH:mm:ss")
    
    $newSheet.Cells.Item(4, 1).Value2 = "数据来源："
    $newSheet.Cells.Item(4, 2).Value2 = $ExcelFile
    
    $newSheet.Cells.Item(5, 1).Value2 = "原始工作表："
    $newSheet.Cells.Item(5, 2).Value2 = $originalSheet.Name
    
    # 数据概览
    $newSheet.Cells.Item(7, 1).Value2 = "数据概览"
    $newSheet.Cells.Item(7, 1).Font.Size = 14
    $newSheet.Cells.Item(7, 1).Font.Bold = $true
    
    $newSheet.Cells.Item(8, 1).Value2 = "总数据量："
    $newSheet.Cells.Item(8, 2).Value2 = $totalCount
    
    $newSheet.Cells.Item(9, 1).Value2 = "唯一值数量："
    $newSheet.Cells.Item(9, 2).Value2 = $valueCount.Count
    
    $newSheet.Cells.Item(10, 1).Value2 = "空值数量："
    $newSheet.Cells.Item(10, 2).Value2 = $emptyCount
    
    # 详细统计表格标题
    $startRow = 13
    $newSheet.Cells.Item($startRow, 1).Value2 = "详细统计结果"
    $newSheet.Cells.Item($startRow, 1).Font.Size = 14
    $newSheet.Cells.Item($startRow, 1).Font.Bold = $true
    
    # 表格标题行
    $headerRow = $startRow + 2
    $newSheet.Cells.Item($headerRow, 1).Value2 = "排名"
    $newSheet.Cells.Item($headerRow, 2).Value2 = "摄影类型"
    $newSheet.Cells.Item($headerRow, 3).Value2 = "数量"
    $newSheet.Cells.Item($headerRow, 4).Value2 = "占比(%)"
    
    # 设置表头格式
    $headerRange = $newSheet.Range($newSheet.Cells.Item($headerRow, 1), $newSheet.Cells.Item($headerRow, 4))
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # 浅蓝色背景
    $headerRange.Borders.LineStyle = 1
    
    # 填入统计数据
    $currentRow = $headerRow + 1
    $rank = 1
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($currentRow, 1).Value2 = $rank
        $newSheet.Cells.Item($currentRow, 2).Value2 = $item.Key
        $newSheet.Cells.Item($currentRow, 3).Value2 = $item.Value
        $newSheet.Cells.Item($currentRow, 4).Value2 = $percentage
        
        # 设置数据格式
        $dataRange = $newSheet.Range($newSheet.Cells.Item($currentRow, 1), $newSheet.Cells.Item($currentRow, 4))
        $dataRange.Borders.LineStyle = 1
        
        # 为前10名设置特殊颜色
        if ($rank -le 10) {
            $dataRange.Interior.Color = 16777164  # 浅黄色背景
        }
        
        $currentRow++
        $rank++
    }
    
    # 添加TOP 10汇总
    $summaryStartRow = $headerRow + $sortedResults.Count + 3
    $newSheet.Cells.Item($summaryStartRow, 1).Value2 = "TOP 10 摄影类型汇总"
    $newSheet.Cells.Item($summaryStartRow, 1).Font.Size = 14
    $newSheet.Cells.Item($summaryStartRow, 1).Font.Bold = $true
    
    $summaryHeaderRow = $summaryStartRow + 2
    $newSheet.Cells.Item($summaryHeaderRow, 1).Value2 = "排名"
    $newSheet.Cells.Item($summaryHeaderRow, 2).Value2 = "摄影类型"
    $newSheet.Cells.Item($summaryHeaderRow, 3).Value2 = "数量"
    $newSheet.Cells.Item($summaryHeaderRow, 4).Value2 = "占比(%)"
    
    # 设置TOP 10表头格式
    $summaryHeaderRange = $newSheet.Range($newSheet.Cells.Item($summaryHeaderRow, 1), $newSheet.Cells.Item($summaryHeaderRow, 4))
    $summaryHeaderRange.Font.Bold = $true
    $summaryHeaderRange.Interior.Color = 5287936  # 绿色背景
    $summaryHeaderRange.Borders.LineStyle = 1
    
    # 填入TOP 10数据
    $summaryCurrentRow = $summaryHeaderRow + 1
    $top10 = $sortedResults | Select-Object -First 10
    $rank = 1
    
    foreach ($item in $top10) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        
        $newSheet.Cells.Item($summaryCurrentRow, 1).Value2 = $rank
        $newSheet.Cells.Item($summaryCurrentRow, 2).Value2 = $item.Key
        $newSheet.Cells.Item($summaryCurrentRow, 3).Value2 = $item.Value
        $newSheet.Cells.Item($summaryCurrentRow, 4).Value2 = $percentage
        
        # 设置数据格式
        $summaryDataRange = $newSheet.Range($newSheet.Cells.Item($summaryCurrentRow, 1), $newSheet.Cells.Item($summaryCurrentRow, 4))
        $summaryDataRange.Borders.LineStyle = 1
        $summaryDataRange.Interior.Color = 13434828  # 浅绿色背景
        
        $summaryCurrentRow++
        $rank++
    }
    
    # 自动调整列宽
    $newSheet.Columns("A:D").AutoFit()
    
    # 设置列宽（如果自动调整不够）
    $newSheet.Columns("B").ColumnWidth = 35  # 摄影类型列稍宽一些
    
    # 激活新创建的工作表
    $newSheet.Activate()
    
    # 保存工作簿
    $workbook.Save()
    
    Write-Host "Successfully added analysis sheet to Excel file!" -ForegroundColor Green
    Write-Host "Sheet name: $sheetName" -ForegroundColor Yellow
    Write-Host "Total records analyzed: $totalCount" -ForegroundColor Yellow
    Write-Host "Unique photography types: $($valueCount.Count)" -ForegroundColor Yellow
    
    # 关闭Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error during processing: $($_.Exception.Message)" -ForegroundColor Red
    
    # 确保Excel进程被关闭
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {
        # 忽略清理错误
    }
    
    exit 1
}

Write-Host ""
Write-Host "Analysis sheet creation completed!" -ForegroundColor Green
