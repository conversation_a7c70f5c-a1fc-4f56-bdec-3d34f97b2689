# Final attempt to create Excel with pie chart
$ErrorActionPreference = "Stop"

Write-Host "开始创建Excel文件..." -ForegroundColor Green

try {
    # Create Excel application
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    Write-Host "Excel应用程序已启动" -ForegroundColor Yellow
    
    # Create workbook
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "摄影分类统计"
    Write-Host "工作簿已创建" -ForegroundColor Yellow
    
    # Add title
    $worksheet.Cells.Item(1, 1) = "摄影分类统计分析"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Add data headers
    $worksheet.Cells.Item(3, 1) = "分类"
    $worksheet.Cells.Item(3, 2) = "计数"
    $worksheet.Cells.Item(3, 1).Font.Bold = $true
    $worksheet.Cells.Item(3, 2).Font.Bold = $true
    
    # Add data
    $worksheet.Cells.Item(4, 1) = "人像摄影"
    $worksheet.Cells.Item(4, 2) = 534
    $worksheet.Cells.Item(5, 1) = "婚礼摄影"
    $worksheet.Cells.Item(5, 2) = 145
    $worksheet.Cells.Item(6, 1) = "其他"
    $worksheet.Cells.Item(6, 2) = 136
    $worksheet.Cells.Item(7, 1) = "商业摄影"
    $worksheet.Cells.Item(7, 2) = 118
    $worksheet.Cells.Item(8, 1) = "儿童摄影"
    $worksheet.Cells.Item(8, 2) = 117
    $worksheet.Cells.Item(9, 1) = "风景摄影"
    $worksheet.Cells.Item(9, 2) = 99
    $worksheet.Cells.Item(10, 1) = "婚礼跟拍"
    $worksheet.Cells.Item(10, 2) = 69
    $worksheet.Cells.Item(11, 1) = "风光人文"
    $worksheet.Cells.Item(11, 2) = 62
    
    Write-Host "数据已添加" -ForegroundColor Yellow
    
    # Set column widths
    $worksheet.Columns("A:A").ColumnWidth = 15
    $worksheet.Columns("B:B").ColumnWidth = 8
    
    Write-Host "开始创建图表..." -ForegroundColor Yellow
    
    # Create chart
    $dataRange = $worksheet.Range("A3:B11")
    $chartObject = $worksheet.Shapes.AddChart()
    $chart = $chartObject.Chart
    
    Write-Host "图表对象已创建" -ForegroundColor Yellow
    
    # Set chart type to pie
    $chart.ChartType = 5  # xlPie
    
    # Set data source
    $chart.SetSourceData($dataRange)
    
    # Set title
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "摄影分类分布"
    
    Write-Host "图表配置完成" -ForegroundColor Yellow
    
    # Position chart
    $chartObject.Left = 250
    $chartObject.Top = 50
    $chartObject.Width = 400
    $chartObject.Height = 300
    
    # Add data labels
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    
    Write-Host "图表格式设置完成" -ForegroundColor Yellow
    
    # Save file
    $outputFile = "摄影分类统计_饼图.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    
    # Remove existing file if it exists
    if (Test-Path $outputFile) {
        Remove-Item $outputFile -Force
        Write-Host "已删除现有文件" -ForegroundColor Gray
    }
    
    $workbook.SaveAs($fullPath)
    Write-Host "文件已保存: $outputFile" -ForegroundColor Green
    
    # Close Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Excel已关闭" -ForegroundColor Yellow
    
    # Verify file exists
    if (Test-Path $outputFile) {
        $fileInfo = Get-Item $outputFile
        Write-Host "`n=== 成功! ===" -ForegroundColor Green
        Write-Host "文件名: $($fileInfo.Name)" -ForegroundColor White
        Write-Host "大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor White
        Write-Host "创建时间: $($fileInfo.CreationTime)" -ForegroundColor White
        
        Write-Host "`n文件包含:" -ForegroundColor Cyan
        Write-Host "✓ 摄影分类数据表格" -ForegroundColor White
        Write-Host "✓ 饼图（显示百分比）" -ForegroundColor White
        Write-Host "✓ TOP 8 摄影分类" -ForegroundColor White
        
        Write-Host "`nTOP 5 分类:" -ForegroundColor Cyan
        Write-Host "1. 人像摄影: 534次 (26.9%)" -ForegroundColor White
        Write-Host "2. 婚礼摄影: 145次 (7.3%)" -ForegroundColor White
        Write-Host "3. 其他: 136次 (6.9%)" -ForegroundColor White
        Write-Host "4. 商业摄影: 118次 (5.9%)" -ForegroundColor White
        Write-Host "5. 儿童摄影: 117次 (5.9%)" -ForegroundColor White
        
    } else {
        Write-Host "警告: 文件未找到!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`n错误发生:" -ForegroundColor Red
    Write-Host "消息: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "位置: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    # Cleanup
    try {
        if ($workbook) { 
            $workbook.Close($false)
            Write-Host "工作簿已关闭" -ForegroundColor Gray
        }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
            Write-Host "Excel已退出" -ForegroundColor Gray
        }
    } catch {
        Write-Host "清理完成" -ForegroundColor Gray
    }
}

Write-Host "`n脚本执行完成!" -ForegroundColor Green
