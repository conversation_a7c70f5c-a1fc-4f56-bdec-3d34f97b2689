# Simple PowerShell script to analyze Excel column D
param(
    [string]$ExcelFile = ""
)

# Find Excel file if not specified
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found in current directory" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
    Write-Host "Using Excel file: $ExcelFile" -ForegroundColor Yellow
}

if (-not (Test-Path $ExcelFile)) {
    Write-Host "Error: Excel file not found: $ExcelFile" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $worksheet = $workbook.ActiveSheet
    
    Write-Host "Worksheet name: $($worksheet.Name)" -ForegroundColor Yellow
    
    $lastRow = $worksheet.UsedRange.Rows.Count
    Write-Host "Total rows: $lastRow" -ForegroundColor Yellow
    
    # Read column D data (starting from row 2, skip header)
    $columnData = @()
    $emptyCount = 0
    
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $worksheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $columnData += $cellValue.ToString().Trim()
        } else {
            $emptyCount++
        }
    }
    
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    if ($columnData.Count -eq 0) {
        Write-Host "No valid data in column D" -ForegroundColor Red
        exit 1
    }
    
    # Count values
    $valueCount = @{}
    foreach ($value in $columnData) {
        if ($valueCount.ContainsKey($value)) {
            $valueCount[$value]++
        } else {
            $valueCount[$value] = 1
        }
    }
    
    $totalCount = $columnData.Count
    
    Write-Host ""
    Write-Host "=== Column D Analysis Results ===" -ForegroundColor Cyan
    Write-Host "Total data count: $totalCount" -ForegroundColor Green
    Write-Host "Unique values: $($valueCount.Count)" -ForegroundColor Green
    Write-Host "Empty cells: $emptyCount" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Detailed statistics:" -ForegroundColor Cyan
    Write-Host "------------------------------------------------------------"
    Write-Host "Value                     Count      Percentage(%)"
    Write-Host "------------------------------------------------------------"
    
    $sortedResults = $valueCount.GetEnumerator() | Sort-Object Value -Descending
    $results = @()
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        $line = "{0,-25} {1,-10} {2,-10}" -f $item.Key, $item.Value, $percentage
        Write-Host $line
        
        $results += [PSCustomObject]@{
            Value = $item.Key
            Count = $item.Value
            Percentage = $percentage
        }
    }
    
    # Save results
    $results | Export-Csv -Path "column_d_results.csv" -NoTypeInformation -Encoding UTF8
    Write-Host ""
    Write-Host "Results saved to: column_d_results.csv" -ForegroundColor Green
    
    # Save to text file
    $outputText = "Column D Analysis Results`n"
    $outputText += "==============================`n`n"
    $outputText += "Total data count: $totalCount`n"
    $outputText += "Unique values: $($valueCount.Count)`n"
    $outputText += "Empty cells: $emptyCount`n`n"
    $outputText += "Detailed statistics:`n"
    $outputText += "------------------------------------------------------------`n"
    $outputText += "Value                     Count      Percentage(%)`n"
    $outputText += "------------------------------------------------------------`n"

    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCount) * 100, 2)
        $line = "{0,-25} {1,-10} {2,-10}" -f $item.Key, $item.Value, $percentage
        $outputText += "$line`n"
    }
    
    $outputText | Out-File -FilePath "column_d_results.txt" -Encoding UTF8
    Write-Host "Results saved to: column_d_results.txt" -ForegroundColor Green
    
} catch {
    Write-Host "Error during analysis: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {
        # Ignore cleanup errors
    }
    
    exit 1
}

Write-Host ""
Write-Host "Analysis completed!" -ForegroundColor Green
