# Excel D列数据统计分析工具

这个工具可以帮您统计Excel文件中D列的数据数量和占比。

## 文件说明

- `analyze_column_d.py` - 主要的Python分析脚本
- `requirements.txt` - Python依赖包列表
- `乱川线下活动数据表格【成都场】20250726.xlsx` - 要分析的Excel文件

## 使用方法

### 方法1：使用Python（推荐）

1. 首先安装Python（如果还没有安装）
   - 访问 https://www.python.org/downloads/ 下载并安装Python
   - 或者安装Anaconda：https://www.anaconda.com/products/distribution

2. 安装依赖包：
   ```bash
   pip install openpyxl
   ```
   
   或者使用requirements.txt：
   ```bash
   pip install -r requirements.txt
   ```

3. 运行分析脚本：
   ```bash
   python analyze_column_d.py
   ```

### 方法2：手动分析（如果Python不可用）

如果无法安装Python，您可以：

1. 打开Excel文件
2. 选中D列的所有数据
3. 使用Excel的数据透视表功能：
   - 插入 → 数据透视表
   - 将D列字段拖到"行"区域
   - 将D列字段拖到"值"区域（会自动计算计数）
   - 右键点击值区域，选择"值字段设置"，可以看到计数和百分比

## 输出结果

脚本运行后会生成：

1. **控制台输出**：显示详细的统计结果
2. **column_d_analysis_results.txt**：保存统计结果的文本文件
3. **column_d_analysis.png**：可视化图表（如果matplotlib可用）

## 统计内容

- D列中每个不同值的出现次数
- 每个值占总数的百分比
- 总数据量和唯一值数量

## 示例输出

```
=== D列统计结果 ===
总数据量: 150
唯一值数量: 5

详细统计:
--------------------------------------------------
值                    数量        占比(%)    
--------------------------------------------------
选项A                 60          40.0       
选项B                 45          30.0       
选项C                 30          20.0       
选项D                 10          6.67       
选项E                 5           3.33       
```

## 注意事项

1. 脚本会自动跳过空值
2. 默认从第2行开始读取（跳过标题行）
3. 如果D列不在第4列位置，可能需要调整代码中的列索引
4. 支持.xlsx和.xls格式的Excel文件

## 故障排除

如果遇到问题：

1. **文件路径错误**：确保Excel文件在同一目录下
2. **编码问题**：确保文件名没有特殊字符
3. **权限问题**：确保有读取Excel文件的权限
4. **依赖包问题**：确保已安装openpyxl包

如需帮助，请检查错误信息并相应调整。
