Write-Host "Testing Excel availability..." -ForegroundColor Yellow

try {
    $excel = New-Object -ComObject Excel.Application
    Write-Host "Excel COM object created successfully" -ForegroundColor Green
    
    $excel.Visible = $false
    $workbook = $excel.Workbooks.Add()
    Write-Host "Workbook created" -ForegroundColor Green
    
    $worksheet = $workbook.ActiveSheet
    $worksheet.Cells.Item(1, 1) = "Test"
    Write-Host "Data added to cell" -ForegroundColor Green
    
    $outputPath = Join-Path (Get-Location) "test_output.xlsx"
    $workbook.SaveAs($outputPath)
    Write-Host "File saved to: $outputPath" -ForegroundColor Green
    
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "Excel test completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Excel test failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
