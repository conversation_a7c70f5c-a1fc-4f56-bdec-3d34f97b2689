# Simple script to analyze split categories
param(
    [string]$ExcelFile = ""
)

# Find Excel file
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" -and $_.Name -notlike "*分析*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $originalSheet = $workbook.ActiveSheet
    
    # Read data from column D
    $lastRow = $originalSheet.UsedRange.Rows.Count
    $rawData = @()
    
    Write-Host "Reading data from column D..." -ForegroundColor Yellow
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $rawData += $cellValue.ToString().Trim()
        }
    }
    
    Write-Host "Total raw entries: $($rawData.Count)" -ForegroundColor Yellow
    
    # Close Excel first
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    # Process data
    $categoryCount = @{}
    $totalCategories = 0
    
    Write-Host "Processing and splitting categories..." -ForegroundColor Yellow
    
    foreach ($entry in $rawData) {
        # Split by common separators
        $categories = @()
        
        # Try different splitting patterns
        if ($entry -match '\+') {
            $categories = $entry -split '\+' | ForEach-Object { $_.Trim() }
        } elseif ($entry -match '、') {
            $categories = $entry -split '、' | ForEach-Object { $_.Trim() }
        } elseif ($entry -match '，') {
            $categories = $entry -split '，' | ForEach-Object { $_.Trim() }
        } else {
            $categories = @($entry.Trim())
        }
        
        foreach ($category in $categories) {
            $cleanCategory = $category.Trim()
            if ($cleanCategory.Length -gt 1) {
                if ($categoryCount.ContainsKey($cleanCategory)) {
                    $categoryCount[$cleanCategory]++
                } else {
                    $categoryCount[$cleanCategory] = 1
                }
                $totalCategories++
            }
        }
    }
    
    Write-Host "Total individual categories counted: $totalCategories" -ForegroundColor Yellow
    Write-Host "Unique category types: $($categoryCount.Count)" -ForegroundColor Yellow
    
    # Sort results
    $sortedResults = $categoryCount.GetEnumerator() | Sort-Object Value -Descending
    
    # Save to CSV
    $csvData = @()
    $rank = 1
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        $csvData += [PSCustomObject]@{
            Rank = $rank
            Category = $item.Key
            Count = $item.Value
            Percentage = $percentage
        }
        $rank++
    }
    
    $csvData | Export-Csv -Path "split_categories_analysis.csv" -NoTypeInformation -Encoding UTF8
    
    # Display results
    Write-Host "`n=== Split Categories Analysis Results ===" -ForegroundColor Cyan
    Write-Host "Original entries: $($rawData.Count)" -ForegroundColor Green
    Write-Host "Total category instances: $totalCategories" -ForegroundColor Green
    Write-Host "Unique categories: $($categoryCount.Count)" -ForegroundColor Green
    
    Write-Host "`nTOP 15 Photography Categories:" -ForegroundColor Cyan
    Write-Host "------------------------------------------------------------"
    Write-Host "Rank  Category                          Count    Percentage"
    Write-Host "------------------------------------------------------------"
    
    $top15 = $sortedResults | Select-Object -First 15
    $displayRank = 1
    foreach ($item in $top15) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        $line = "{0,-5} {1,-35} {2,-8} {3,-10}" -f $displayRank, $item.Key, $item.Value, "$percentage%"
        Write-Host $line -ForegroundColor White
        $displayRank++
    }
    
    Write-Host "`nResults saved to: split_categories_analysis.csv" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}

Write-Host "`nAnalysis completed!" -ForegroundColor Green
