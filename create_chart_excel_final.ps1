# Final version - Create Excel with pie chart
$outputFile = "摄影分类统计饼图.xlsx"

try {
    Write-Host "=== Creating Excel file with pie chart ===" -ForegroundColor Cyan
    Write-Host "Output file: $outputFile" -ForegroundColor Yellow
    
    # Remove existing file if it exists
    if (Test-Path $outputFile) {
        Remove-Item $outputFile -Force
        Write-Host "Removed existing file" -ForegroundColor Gray
    }
    
    Write-Host "Starting Excel..." -ForegroundColor Green
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    Write-Host "Creating workbook..." -ForegroundColor Green
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "摄影分类统计"
    
    Write-Host "Adding title and metadata..." -ForegroundColor Green
    
    # Title
    $worksheet.Cells.Item(1, 1) = "摄影分类统计分析（拆分版）"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Metadata
    $worksheet.Cells.Item(2, 1) = "分析时间: $(Get-Date -Format 'yyyy年MM月dd日')"
    $worksheet.Cells.Item(3, 1) = "总分类数: 1986个实例"
    $worksheet.Cells.Item(4, 1) = "数据说明: 多分类条目已拆分统计"
    
    Write-Host "Adding chart data..." -ForegroundColor Green
    
    # Chart data headers
    $worksheet.Cells.Item(6, 1) = "摄影分类"
    $worksheet.Cells.Item(6, 2) = "计数"
    $worksheet.Cells.Item(6, 1).Font.Bold = $true
    $worksheet.Cells.Item(6, 2).Font.Bold = $true
    
    # Data (TOP 10 for clear visualization)
    $data = @(
        @("人像摄影", 534),
        @("婚礼摄影", 145),
        @("其他", 136),
        @("商业摄影", 118),
        @("儿童摄影", 117),
        @("风景摄影", 99),
        @("婚礼跟拍", 69),
        @("风光人文", 62),
        @("视频制作", 52),
        @("儿童亲子", 40)
    )
    
    # Add data to worksheet
    $row = 7
    foreach ($item in $data) {
        $worksheet.Cells.Item($row, 1) = $item[0]
        $worksheet.Cells.Item($row, 2) = $item[1]
        $row++
    }
    
    Write-Host "Formatting columns..." -ForegroundColor Green
    $worksheet.Columns("A:A").ColumnWidth = 15
    $worksheet.Columns("B:B").ColumnWidth = 8
    
    Write-Host "Creating pie chart..." -ForegroundColor Green
    
    # Select data range for chart
    $chartRange = $worksheet.Range("A6:B16")
    
    # Create chart
    $chartObj = $worksheet.Shapes.AddChart()
    $chart = $chartObj.Chart
    
    # Configure chart
    $chart.ChartType = 5  # xlPie
    $chart.SetSourceData($chartRange)
    
    # Chart formatting
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "摄影分类分布统计"
    $chart.ChartTitle.Font.Size = 14
    $chart.ChartTitle.Font.Bold = $true
    
    # Position chart
    $chartObj.Left = 200
    $chartObj.Top = 120
    $chartObj.Width = 450
    $chartObj.Height = 350
    
    # Add data labels with percentages
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chart.SeriesCollection(1).DataLabels.ShowCategoryName = $false
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false
    
    # Legend
    $chart.HasLegend = $true
    $chart.Legend.Position = -4152  # Right side
    
    Write-Host "Adding summary statistics..." -ForegroundColor Green
    
    # Add summary below the data
    $summaryRow = $row + 2
    $worksheet.Cells.Item($summaryRow, 1) = "统计摘要:"
    $worksheet.Cells.Item($summaryRow, 1).Font.Bold = $true
    
    $worksheet.Cells.Item($summaryRow + 1, 1) = "• 人像摄影占主导地位 (26.9%)"
    $worksheet.Cells.Item($summaryRow + 2, 1) = "• 前5类占总数的53.9%"
    $worksheet.Cells.Item($summaryRow + 3, 1) = "• 显示多元化发展趋势"
    
    Write-Host "Saving Excel file..." -ForegroundColor Green
    
    # Save the workbook
    $fullPath = Join-Path (Get-Location) $outputFile
    $workbook.SaveAs($fullPath)
    
    Write-Host "Closing Excel..." -ForegroundColor Green
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "`n=== SUCCESS ===" -ForegroundColor Green
    Write-Host "Excel file created: $outputFile" -ForegroundColor Yellow
    Write-Host "Contains: Data table + Pie chart + Summary" -ForegroundColor White
    
    Write-Host "`nChart shows TOP 10 categories:" -ForegroundColor Cyan
    Write-Host "1. 人像摄影: 534次 (26.9%)" -ForegroundColor White
    Write-Host "2. 婚礼摄影: 145次 (7.3%)" -ForegroundColor White
    Write-Host "3. 其他: 136次 (6.9%)" -ForegroundColor White
    Write-Host "4. 商业摄影: 118次 (5.9%)" -ForegroundColor White
    Write-Host "5. 儿童摄影: 117次 (5.9%)" -ForegroundColor White
    
    # Verify file exists
    if (Test-Path $outputFile) {
        $fileInfo = Get-Item $outputFile
        Write-Host "`nFile details:" -ForegroundColor Cyan
        Write-Host "Size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor White
        Write-Host "Created: $($fileInfo.CreationTime)" -ForegroundColor White
    } else {
        Write-Host "Warning: File not found after creation!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "`nERROR occurred:" -ForegroundColor Red
    Write-Host "Message: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    # Cleanup
    try {
        if ($workbook) { 
            $workbook.Close($false)
            Write-Host "Workbook closed" -ForegroundColor Gray
        }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
            Write-Host "Excel application closed" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Cleanup completed" -ForegroundColor Gray
    }
    
    exit 1
}

Write-Host "`nTask completed successfully!" -ForegroundColor Green
