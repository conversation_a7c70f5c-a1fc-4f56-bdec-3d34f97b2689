#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D列数据统计分析脚本
统计D列中各个值的数量和占比
"""

try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("pandas未安装，将使用openpyxl进行基础分析")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("matplotlib未安装，将跳过图表生成")

from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.chart import Pie<PERSON>hart, Reference
import openpyxl.chart.label

def analyze_column_d_openpyxl(file_path, sheet_name=None, column_index=4):
    """
    使用openpyxl分析Excel文件中D列的数据统计

    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称，None表示活动工作表
        column_index (int): 列索引，D列为4

    Returns:
        dict: 包含统计结果的字典
    """
    try:
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)

        if sheet_name:
            worksheet = workbook[sheet_name]
        else:
            worksheet = workbook.active

        print(f"成功读取Excel文件: {file_path}")
        print(f"工作表名称: {worksheet.title}")

        # 读取D列数据
        column_data = []
        max_row = worksheet.max_row

        for row in range(2, max_row + 1):  # 从第2行开始，跳过标题行
            cell_value = worksheet.cell(row=row, column=column_index).value
            if cell_value is not None and str(cell_value).strip():
                column_data.append(str(cell_value).strip())

        if not column_data:
            print("D列没有有效数据")
            return {}

        # 统计各个值的数量
        value_counts = Counter(column_data)
        total_count = len(column_data)

        # 创建统计结果
        results = {
            'total_count': total_count,
            'unique_values': len(value_counts),
            'statistics': []
        }

        print(f"\n=== D列统计结果 ===")
        print(f"总数据量: {total_count}")
        print(f"唯一值数量: {len(value_counts)}")
        print(f"\n详细统计:")
        print("-" * 50)
        print(f"{'值':<20} {'数量':<10} {'占比(%)':<10}")
        print("-" * 50)

        # 按数量排序
        for value, count in value_counts.most_common():
            percentage = round(count / total_count * 100, 2)
            print(f"{value:<20} {count:<10} {percentage:<10}")

            results['statistics'].append({
                'value': value,
                'count': count,
                'percentage': percentage
            })

        workbook.close()
        return results

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        return {}

def analyze_column_d(file_path, sheet_name=None, column_name='D'):
    """
    分析Excel文件中D列的数据统计
    
    Args:
        file_path (str): Excel文件路径
        sheet_name (str): 工作表名称，None表示第一个工作表
        column_name (str): 要分析的列名，默认为'D'
    
    Returns:
        dict: 包含统计结果的字典
    """
    try:
        # 读取Excel文件
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        
        print(f"成功读取Excel文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 获取D列数据（如果列名是字母，尝试按位置获取）
        if column_name in df.columns:
            column_data = df[column_name]
        elif len(df.columns) > 3:  # D列是第4列（索引3）
            column_data = df.iloc[:, 3]  # 第4列
            column_name = df.columns[3]
        else:
            raise ValueError("找不到D列数据")
        
        print(f"\n分析列: {column_name}")
        
        # 移除空值
        column_data_clean = column_data.dropna()
        total_count = len(column_data_clean)
        
        if total_count == 0:
            print("D列没有有效数据")
            return {}
        
        # 统计各个值的数量
        value_counts = column_data_clean.value_counts()
        
        # 计算占比
        value_percentages = (value_counts / total_count * 100).round(2)
        
        # 创建统计结果
        results = {
            'total_count': total_count,
            'unique_values': len(value_counts),
            'statistics': []
        }
        
        print(f"\n=== D列统计结果 ===")
        print(f"总数据量: {total_count}")
        print(f"唯一值数量: {len(value_counts)}")
        print(f"\n详细统计:")
        print("-" * 50)
        print(f"{'值':<20} {'数量':<10} {'占比(%)':<10}")
        print("-" * 50)
        
        for value, count in value_counts.items():
            percentage = value_percentages[value]
            print(f"{str(value):<20} {count:<10} {percentage:<10}")
            
            results['statistics'].append({
                'value': value,
                'count': count,
                'percentage': percentage
            })
        
        # 生成可视化图表
        create_visualization(value_counts, value_percentages, column_name)
        
        return results
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        return {}

def create_visualization(value_counts, value_percentages, column_name):
    """
    创建可视化图表
    """
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 柱状图 - 数量
        ax1.bar(range(len(value_counts)), value_counts.values)
        ax1.set_title(f'{column_name}列 - 数量统计')
        ax1.set_xlabel('类别')
        ax1.set_ylabel('数量')
        ax1.set_xticks(range(len(value_counts)))
        ax1.set_xticklabels(value_counts.index, rotation=45, ha='right')
        
        # 在柱子上显示数值
        for i, v in enumerate(value_counts.values):
            ax1.text(i, v + max(value_counts.values) * 0.01, str(v), 
                    ha='center', va='bottom')
        
        # 饼图 - 占比
        ax2.pie(value_percentages.values, labels=value_percentages.index, 
                autopct='%1.1f%%', startangle=90)
        ax2.set_title(f'{column_name}列 - 占比分布')
        
        plt.tight_layout()
        plt.savefig('column_d_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n图表已保存为: column_d_analysis.png")
        
    except Exception as e:
        print(f"生成图表时出现错误: {str(e)}")

def analyze_split_categories(file_path, sheet_name=None, column_index=4):
    """
    分析D列数据，将多分类条目拆分统计
    """
    try:
        # 打开Excel文件
        workbook = openpyxl.load_workbook(file_path)

        if sheet_name:
            worksheet = workbook[sheet_name]
        else:
            worksheet = workbook.active

        print(f"成功读取Excel文件: {file_path}")
        print(f"工作表名称: {worksheet.title}")

        # 读取D列数据
        raw_data = []
        max_row = worksheet.max_row

        for row in range(2, max_row + 1):  # 从第2行开始，跳过标题行
            cell_value = worksheet.cell(row=row, column=column_index).value
            if cell_value is not None and str(cell_value).strip():
                raw_data.append(str(cell_value).strip())

        if not raw_data:
            print("D列没有有效数据")
            return {}

        print(f"原始条目数: {len(raw_data)}")

        # 拆分分类并统计
        category_count = {}
        total_categories = 0

        for entry in raw_data:
            # 使用多种分隔符拆分
            categories = []

            # 主要分隔符：+
            if '+' in entry:
                categories = [cat.strip() for cat in entry.split('+')]
            # 其他分隔符
            elif '、' in entry:
                categories = [cat.strip() for cat in entry.split('、')]
            elif '，' in entry:
                categories = [cat.strip() for cat in entry.split('，')]
            else:
                categories = [entry.strip()]

            # 统计每个分类
            for category in categories:
                clean_category = category.strip()
                if len(clean_category) > 1:  # 过滤掉太短的分类
                    if clean_category in category_count:
                        category_count[clean_category] += 1
                    else:
                        category_count[clean_category] = 1
                    total_categories += 1

        print(f"总分类实例数: {total_categories}")
        print(f"唯一分类数: {len(category_count)}")

        # 创建结果
        results = {
            'original_entries': len(raw_data),
            'total_categories': total_categories,
            'unique_categories': len(category_count),
            'statistics': []
        }

        print(f"\n=== D列拆分分类统计结果 ===")
        print(f"原始条目数: {len(raw_data)}")
        print(f"总分类实例数: {total_categories}")
        print(f"唯一分类数: {len(category_count)}")
        print(f"\n详细统计:")
        print("-" * 60)
        print(f"{'分类':<30} {'计数':<10} {'占比(%)':<10}")
        print("-" * 60)

        # 按数量排序
        sorted_categories = sorted(category_count.items(), key=lambda x: x[1], reverse=True)

        for category, count in sorted_categories:
            percentage = round(count / total_categories * 100, 2)
            print(f"{category:<30} {count:<10} {percentage:<10}")

            results['statistics'].append({
                'category': category,
                'count': count,
                'percentage': percentage
            })

        workbook.close()
        return results

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        return {}

def create_excel_with_pie_chart(results, output_file="D列统计分析饼图.xlsx"):
    """
    创建包含饼图的Excel文件

    Args:
        results (dict): 分析结果
        output_file (str): 输出文件名

    Returns:
        bool: 是否成功创建
    """
    try:
        print("📊 开始创建包含饼图的Excel文件...")

        # 创建工作簿
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "D列统计分析"

        print("📝 添加标题和数据...")

        # 设置样式

        title_font = Font(size=16, bold=True)
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 添加标题
        worksheet['A1'] = "D列数据统计分析"
        worksheet['A1'].font = title_font

        # 添加元数据
        import datetime
        worksheet['A3'] = f"分析时间: {datetime.datetime.now().strftime('%Y年%m月%d日')}"
        worksheet['A4'] = f"总数据量: {results.get('total_count', 0)}"
        worksheet['A5'] = f"唯一值数量: {results.get('unique_values', 0)}"

        # 数据表格标题
        worksheet['A7'] = "类别"
        worksheet['B7'] = "数量"
        worksheet['C7'] = "占比(%)"

        # 设置表头样式
        for col in ['A7', 'B7', 'C7']:
            worksheet[col].font = header_font
            worksheet[col].fill = header_fill
            worksheet[col].border = border
            worksheet[col].alignment = Alignment(horizontal='center')

        # 添加数据
        statistics = results.get('statistics', [])
        for i, stat in enumerate(statistics, start=8):
            worksheet[f'A{i}'] = str(stat.get('value', ''))
            worksheet[f'B{i}'] = stat.get('count', 0)
            worksheet[f'C{i}'] = stat.get('percentage', 0)

            # 设置边框
            for col in ['A', 'B', 'C']:
                worksheet[f'{col}{i}'].border = border

        print("📊 创建饼图...")

        # 创建饼图
        pie_chart = PieChart()
        pie_chart.title = "D列数据分布统计"

        # 设置数据范围
        data_end_row = 7 + len(statistics)
        labels = Reference(worksheet, min_col=1, min_row=8, max_row=data_end_row)
        data_ref = Reference(worksheet, min_col=2, min_row=8, max_row=data_end_row)

        pie_chart.add_data(data_ref)
        pie_chart.set_categories(labels)

        # 设置数据标签显示百分比
        pie_chart.dataLabels = openpyxl.chart.label.DataLabelList()
        pie_chart.dataLabels.showPercent = True
        pie_chart.dataLabels.showVal = False
        pie_chart.dataLabels.showCatName = False

        # 设置图例
        pie_chart.legend.position = 'r'  # 右侧

        # 将图表添加到工作表
        worksheet.add_chart(pie_chart, "E7")

        print("🎨 设置格式...")

        # 调整列宽
        worksheet.column_dimensions['A'].width = 20
        worksheet.column_dimensions['B'].width = 10
        worksheet.column_dimensions['C'].width = 12

        print("💾 保存Excel文件...")

        # 保存文件
        workbook.save(output_file)
        workbook.close()

        print(f"✅ 成功创建Excel文件: {output_file}")
        print(f"📈 包含内容:")
        print(f"   - 数据表格 ({len(statistics)}个类别)")
        print(f"   - 饼图 (带百分比标签)")

        return True

    except Exception as e:
        print(f"❌ 创建Excel文件时出现错误: {str(e)}")
        return False

def main():
    """
    主函数
    """
    # Excel文件路径
    file_path = "乱川线下活动数据表格20250726.xlsx"

    print("开始分析D列数据...")

    # 首先尝试使用pandas分析
    if PANDAS_AVAILABLE:
        print("使用pandas进行分析...")
        results = analyze_column_d(file_path)
    else:
        print("使用openpyxl进行基础分析...")
        results = analyze_column_d_openpyxl(file_path)

    if results:
        print("\n分析完成！")

        # 保存结果到文件
        save_results_to_file(results)

        # 创建包含饼图的Excel文件
        create_excel_with_pie_chart(results)
    else:
        print("分析失败，请检查文件路径和数据格式")

def save_split_results_to_file(results):
    """
    将拆分分类结果保存到文件
    """
    try:
        # 保存到文本文件
        with open('split_categories_analysis.txt', 'w', encoding='utf-8') as f:
            f.write("D列拆分分类统计分析结果\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"原始条目数: {results['original_entries']}\n")
            f.write(f"总分类实例数: {results['total_categories']}\n")
            f.write(f"唯一分类数: {results['unique_categories']}\n\n")
            f.write("详细统计:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'分类':<30} {'计数':<10} {'占比(%)':<10}\n")
            f.write("-" * 60 + "\n")

            for stat in results['statistics']:
                f.write(f"{stat['category']:<30} {stat['count']:<10} {stat['percentage']:<10}\n")

        print("结果已保存到: split_categories_analysis.txt")

        # 保存到CSV文件
        try:
            import csv
            with open('split_categories_analysis.csv', 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['Rank', 'Category', 'Count', 'Percentage']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for rank, stat in enumerate(results['statistics'], 1):
                    writer.writerow({
                        'Rank': rank,
                        'Category': stat['category'],
                        'Count': stat['count'],
                        'Percentage': stat['percentage']
                    })

            print("结果已保存到: split_categories_analysis.csv")

        except Exception as e:
            print(f"保存CSV文件时出现错误: {str(e)}")

    except Exception as e:
        print(f"保存结果时出现错误: {str(e)}")

def save_results_to_file(results):
    """
    将结果保存到文件
    """
    try:
        with open('column_d_analysis_results.txt', 'w', encoding='utf-8') as f:
            f.write("D列统计分析结果\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"总数据量: {results['total_count']}\n")
            f.write(f"唯一值数量: {results['unique_values']}\n\n")
            f.write("详细统计:\n")
            f.write("-" * 50 + "\n")
            f.write(f"{'值':<20} {'数量':<10} {'占比(%)':<10}\n")
            f.write("-" * 50 + "\n")

            for stat in results['statistics']:
                f.write(f"{str(stat['value']):<20} {stat['count']:<10} {stat['percentage']:<10}\n")

        print("结果已保存到: column_d_analysis_results.txt")

    except Exception as e:
        print(f"保存结果时出现错误: {str(e)}")

if __name__ == "__main__":
    main()
