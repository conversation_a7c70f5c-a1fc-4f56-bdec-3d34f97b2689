# 权限问题解决方案

## 🔍 问题诊断

当前遇到的权限问题主要有以下几种可能：

1. **PowerShell执行策略限制**
2. **用户账户控制(UAC)限制**
3. **脚本执行权限不足**
4. **Excel COM组件访问权限**

## 🛠️ 解决方案（按推荐顺序）

### 方案1：临时修改PowerShell执行策略 ⭐⭐⭐⭐⭐

**步骤：**
1. 右键点击"开始"按钮 → 选择"Windows PowerShell (管理员)"
2. 在管理员PowerShell中执行：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. 输入 `Y` 确认
4. 然后运行我们的脚本：
   ```powershell
   cd "d:\augment_project\xls_magic"
   .\create_pie_chart_fixed.ps1
   ```

### 方案2：使用管理员权限运行 ⭐⭐⭐⭐

**步骤：**
1. 右键点击"开始"按钮 → 选择"命令提示符(管理员)"
2. 在管理员命令提示符中执行：
   ```cmd
   cd /d "d:\augment_project\xls_magic"
   powershell -ExecutionPolicy Bypass -File create_pie_chart_fixed.ps1
   ```

### 方案3：直接在PowerShell中执行代码 ⭐⭐⭐⭐

**步骤：**
1. 以管理员身份打开PowerShell
2. 直接复制粘贴以下代码并回车：

```powershell
cd "d:\augment_project\xls_magic"
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$excel.DisplayAlerts = $false
$workbook = $excel.Workbooks.Add()
$worksheet = $workbook.ActiveSheet
$worksheet.Name = "摄影分类统计"

# 添加标题
$worksheet.Cells.Item(1, 1) = "摄影分类统计分析（拆分版）"
$worksheet.Cells.Item(1, 1).Font.Size = 16
$worksheet.Cells.Item(1, 1).Font.Bold = $true

# 添加表头
$worksheet.Cells.Item(3, 1) = "摄影分类"
$worksheet.Cells.Item(3, 2) = "计数"
$worksheet.Cells.Item(3, 1).Font.Bold = $true
$worksheet.Cells.Item(3, 2).Font.Bold = $true

# 添加数据
$worksheet.Cells.Item(4, 1) = "人像摄影"; $worksheet.Cells.Item(4, 2) = 534
$worksheet.Cells.Item(5, 1) = "婚礼摄影"; $worksheet.Cells.Item(5, 2) = 145
$worksheet.Cells.Item(6, 1) = "其他"; $worksheet.Cells.Item(6, 2) = 136
$worksheet.Cells.Item(7, 1) = "商业摄影"; $worksheet.Cells.Item(7, 2) = 118
$worksheet.Cells.Item(8, 1) = "儿童摄影"; $worksheet.Cells.Item(8, 2) = 117
$worksheet.Cells.Item(9, 1) = "风景摄影"; $worksheet.Cells.Item(9, 2) = 99
$worksheet.Cells.Item(10, 1) = "婚礼跟拍"; $worksheet.Cells.Item(10, 2) = 69
$worksheet.Cells.Item(11, 1) = "风光人文"; $worksheet.Cells.Item(11, 2) = 62

# 设置列宽
$worksheet.Columns("A:A").ColumnWidth = 18
$worksheet.Columns("B:B").ColumnWidth = 10

# 创建饼图
$dataRange = $worksheet.Range("A3:B11")
$chartObject = $worksheet.Shapes.AddChart()
$chart = $chartObject.Chart
$chart.ChartType = 5
$chart.SetSourceData($dataRange)
$chart.HasTitle = $true
$chart.ChartTitle.Text = "摄影分类分布统计"
$chartObject.Left = 300
$chartObject.Top = 50
$chartObject.Width = 400
$chartObject.Height = 300
$chart.SeriesCollection(1).HasDataLabels = $true
$chart.SeriesCollection(1).DataLabels.ShowPercentage = $true

# 保存文件
$outputFile = "摄影分类统计_饼图.xlsx"
$fullPath = Join-Path (Get-Location) $outputFile
if (Test-Path $outputFile) { Remove-Item $outputFile -Force }
$workbook.SaveAs($fullPath)
$workbook.Close($false)
$excel.Quit()
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null

Write-Host "成功创建: $outputFile" -ForegroundColor Green
```

### 方案4：修改注册表启用脚本执行 ⭐⭐⭐

**注意：此方案需要管理员权限，请谨慎操作**

1. 按 `Win + R`，输入 `regedit`，以管理员身份运行
2. 导航到：`HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell`
3. 找到 `ExecutionPolicy` 项，双击修改值为 `RemoteSigned`
4. 如果不存在该项，右键创建新的字符串值

### 方案5：使用批处理文件绕过权限 ⭐⭐⭐

创建一个新的批处理文件，使用不同的方法：

```batch
@echo off
echo 正在创建Excel文件...
powershell -NoProfile -ExecutionPolicy Bypass -Command "& {[System.Reflection.Assembly]::LoadWithPartialName('Microsoft.Office.Interop.Excel') | Out-Null; $excel = New-Object -ComObject Excel.Application; $excel.Visible = $false; $workbook = $excel.Workbooks.Add(); $worksheet = $workbook.ActiveSheet; $worksheet.Cells.Item(1,1) = 'Photography Analysis'; $worksheet.Cells.Item(2,1) = 'Category'; $worksheet.Cells.Item(2,2) = 'Count'; $worksheet.Cells.Item(3,1) = 'Portrait'; $worksheet.Cells.Item(3,2) = 534; $worksheet.Cells.Item(4,1) = 'Wedding'; $worksheet.Cells.Item(4,2) = 145; $range = $worksheet.Range('A2:B4'); $chart = $worksheet.Shapes.AddChart().Chart; $chart.ChartType = 5; $chart.SetSourceData($range); $chart.HasTitle = $true; $chart.ChartTitle.Text = 'Distribution'; $workbook.SaveAs((Get-Location).Path + '\PhotoChart.xlsx'); $workbook.Close(); $excel.Quit(); Write-Host 'Created PhotoChart.xlsx'}"
pause
```

### 方案6：使用Windows Script Host ⭐⭐

如果PowerShell完全被阻止，可以使用WSH：

1. 右键点击 `生成饼图.vbs` 文件
2. 选择"以管理员身份运行"
3. 或者在命令提示符中：`wscript 生成饼图.vbs`

## 🔧 权限检查工具

让我创建一个权限检查脚本：
