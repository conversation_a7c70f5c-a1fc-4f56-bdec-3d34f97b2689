# Simple pie chart creation
$outputFile = "摄影分类饼图.xlsx"

Write-Host "开始创建Excel文件..." -ForegroundColor Green

try {
    # 创建Excel应用
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # 创建工作簿
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "分类统计"
    
    # 添加标题
    $worksheet.Cells.Item(1, 1) = "摄影分类统计分析"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # 添加数据标题
    $worksheet.Cells.Item(3, 1) = "分类"
    $worksheet.Cells.Item(3, 2) = "计数"
    $worksheet.Cells.Item(3, 1).Font.Bold = $true
    $worksheet.Cells.Item(3, 2).Font.Bold = $true
    
    # 添加数据
    $worksheet.Cells.Item(4, 1) = "人像摄影"
    $worksheet.Cells.Item(4, 2) = 534
    $worksheet.Cells.Item(5, 1) = "婚礼摄影"
    $worksheet.Cells.Item(5, 2) = 145
    $worksheet.Cells.Item(6, 1) = "其他"
    $worksheet.Cells.Item(6, 2) = 136
    $worksheet.Cells.Item(7, 1) = "商业摄影"
    $worksheet.Cells.Item(7, 2) = 118
    $worksheet.Cells.Item(8, 1) = "儿童摄影"
    $worksheet.Cells.Item(8, 2) = 117
    $worksheet.Cells.Item(9, 1) = "风景摄影"
    $worksheet.Cells.Item(9, 2) = 99
    $worksheet.Cells.Item(10, 1) = "婚礼跟拍"
    $worksheet.Cells.Item(10, 2) = 69
    $worksheet.Cells.Item(11, 1) = "风光人文"
    $worksheet.Cells.Item(11, 2) = 62
    
    # 设置列宽
    $worksheet.Columns("A:A").ColumnWidth = 15
    $worksheet.Columns("B:B").ColumnWidth = 8
    
    Write-Host "创建饼图..." -ForegroundColor Yellow
    
    # 选择数据范围
    $dataRange = $worksheet.Range("A3:B11")
    
    # 创建图表
    $chartObject = $worksheet.Shapes.AddChart()
    $chart = $chartObject.Chart
    
    # 设置为饼图
    $chart.ChartType = 5  # xlPie
    
    # 设置数据源
    $chart.SetSourceData($dataRange)
    
    # 设置标题
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "摄影分类分布"
    
    # 设置位置
    $chartObject.Left = 200
    $chartObject.Top = 50
    $chartObject.Width = 400
    $chartObject.Height = 300
    
    # 添加数据标签
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    
    Write-Host "保存文件..." -ForegroundColor Yellow
    
    # 保存文件
    $fullPath = Join-Path (Get-Location) $outputFile
    if (Test-Path $outputFile) {
        Remove-Item $outputFile -Force
    }
    $workbook.SaveAs($fullPath)
    
    # 关闭
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "成功创建: $outputFile" -ForegroundColor Green
    
    if (Test-Path $outputFile) {
        Write-Host "文件已确认存在" -ForegroundColor Green
    }
    
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
}

Write-Host "脚本完成" -ForegroundColor Green
