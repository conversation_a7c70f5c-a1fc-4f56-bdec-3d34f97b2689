# Simple script to create Excel file from CSV data
$OutputFile = "D列统计分析结果.xlsx"

try {
    if (Test-Path "column_d_results.csv") {
        Write-Host "Reading CSV data..." -ForegroundColor Green
        $csvData = Import-Csv "column_d_results.csv"
        
        Write-Host "Creating Excel application..." -ForegroundColor Yellow
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        
        Write-Host "Creating new workbook..." -ForegroundColor Yellow
        $workbook = $excel.Workbooks.Add()
        $worksheet = $workbook.ActiveSheet
        $worksheet.Name = "统计分析"
        
        # Add title
        $worksheet.Cells.Item(1, 1) = "D列摄影类型统计分析"
        $worksheet.Cells.Item(1, 1).Font.Size = 16
        $worksheet.Cells.Item(1, 1).Font.Bold = $true
        
        # Add headers
        $worksheet.Cells.Item(3, 1) = "排名"
        $worksheet.Cells.Item(3, 2) = "摄影类型"
        $worksheet.Cells.Item(3, 3) = "数量"
        $worksheet.Cells.Item(3, 4) = "占比(%)"
        
        # Format headers
        for ($col = 1; $col -le 4; $col++) {
            $worksheet.Cells.Item(3, $col).Font.Bold = $true
        }
        
        # Add data
        $row = 4
        $rank = 1
        foreach ($item in $csvData) {
            $worksheet.Cells.Item($row, 1) = $rank
            $worksheet.Cells.Item($row, 2) = $item.Value
            $worksheet.Cells.Item($row, 3) = $item.Count
            $worksheet.Cells.Item($row, 4) = $item.Percentage
            $row++
            $rank++
        }
        
        # Auto-fit columns
        $worksheet.Columns("B:B").ColumnWidth = 40
        
        Write-Host "Saving workbook..." -ForegroundColor Yellow
        $fullPath = Join-Path (Get-Location) $OutputFile
        $workbook.SaveAs($fullPath)
        
        Write-Host "Closing Excel..." -ForegroundColor Yellow
        $workbook.Close($false)
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        
        Write-Host "Successfully created: $OutputFile" -ForegroundColor Green
        Write-Host "Total records: $($csvData.Count)" -ForegroundColor Yellow
        
    } else {
        Write-Host "CSV file not found!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
}
