@echo off
chcp 65001 >nul
echo ========================================
echo 一键生成摄影分类统计饼图
echo ========================================
echo.
echo 正在创建Excel文件，请稍候...
echo.

powershell -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -Command ^
"try { ^
    $excel = New-Object -ComObject Excel.Application; ^
    $excel.Visible = $false; ^
    $excel.DisplayAlerts = $false; ^
    $workbook = $excel.Workbooks.Add(); ^
    $worksheet = $workbook.ActiveSheet; ^
    $worksheet.Name = '摄影分类统计'; ^
    $worksheet.Cells.Item(1, 1) = '摄影分类统计分析（拆分版）'; ^
    $worksheet.Cells.Item(1, 1).Font.Size = 16; ^
    $worksheet.Cells.Item(1, 1).Font.Bold = $true; ^
    $worksheet.Cells.Item(2, 1) = '分析时间: 2025年7月26日'; ^
    $worksheet.Cells.Item(3, 1) = '总分类数: 1986个实例'; ^
    $worksheet.Cells.Item(5, 1) = '摄影分类'; ^
    $worksheet.Cells.Item(5, 2) = '计数'; ^
    $worksheet.Cells.Item(5, 1).Font.Bold = $true; ^
    $worksheet.Cells.Item(5, 2).Font.Bold = $true; ^
    $worksheet.Cells.Item(6, 1) = '人像摄影'; $worksheet.Cells.Item(6, 2) = 534; ^
    $worksheet.Cells.Item(7, 1) = '婚礼摄影'; $worksheet.Cells.Item(7, 2) = 145; ^
    $worksheet.Cells.Item(8, 1) = '其他'; $worksheet.Cells.Item(8, 2) = 136; ^
    $worksheet.Cells.Item(9, 1) = '商业摄影'; $worksheet.Cells.Item(9, 2) = 118; ^
    $worksheet.Cells.Item(10, 1) = '儿童摄影'; $worksheet.Cells.Item(10, 2) = 117; ^
    $worksheet.Cells.Item(11, 1) = '风景摄影'; $worksheet.Cells.Item(11, 2) = 99; ^
    $worksheet.Cells.Item(12, 1) = '婚礼跟拍'; $worksheet.Cells.Item(12, 2) = 69; ^
    $worksheet.Cells.Item(13, 1) = '风光人文'; $worksheet.Cells.Item(13, 2) = 62; ^
    $worksheet.Cells.Item(14, 1) = '视频制作'; $worksheet.Cells.Item(14, 2) = 52; ^
    $worksheet.Cells.Item(15, 1) = '儿童亲子'; $worksheet.Cells.Item(15, 2) = 40; ^
    $worksheet.Columns('A:A').ColumnWidth = 18; ^
    $worksheet.Columns('B:B').ColumnWidth = 10; ^
    $dataRange = $worksheet.Range('A5:B15'); ^
    $chartObject = $worksheet.Shapes.AddChart(); ^
    $chart = $chartObject.Chart; ^
    $chart.ChartType = 5; ^
    $chart.SetSourceData($dataRange); ^
    $chart.HasTitle = $true; ^
    $chart.ChartTitle.Text = '摄影分类分布统计'; ^
    $chartObject.Left = 300; ^
    $chartObject.Top = 100; ^
    $chartObject.Width = 450; ^
    $chartObject.Height = 350; ^
    $chart.SeriesCollection(1).HasDataLabels = $true; ^
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true; ^
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false; ^
    $worksheet.Cells.Item(17, 1) = '统计摘要:'; ^
    $worksheet.Cells.Item(17, 1).Font.Bold = $true; ^
    $worksheet.Cells.Item(18, 1) = '• 人像摄影占主导地位 (26.9%%)'; ^
    $worksheet.Cells.Item(19, 1) = '• 前5类占总数的53.9%%'; ^
    $worksheet.Cells.Item(20, 1) = '• 显示多元化发展趋势'; ^
    $outputFile = '摄影分类统计_饼图.xlsx'; ^
    $fullPath = Join-Path (Get-Location) $outputFile; ^
    if (Test-Path $outputFile) { Remove-Item $outputFile -Force }; ^
    $workbook.SaveAs($fullPath); ^
    $workbook.Close($false); ^
    $excel.Quit(); ^
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null; ^
    Write-Host '成功创建Excel文件!' -ForegroundColor Green; ^
    Write-Host '文件名: $outputFile' -ForegroundColor Yellow; ^
    if (Test-Path $outputFile) { ^
        $fileInfo = Get-Item $outputFile; ^
        Write-Host '文件大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB' -ForegroundColor Yellow; ^
        Write-Host '创建时间: $($fileInfo.CreationTime)' -ForegroundColor Yellow; ^
    } ^
} catch { ^
    Write-Host '错误: $($_.Exception.Message)' -ForegroundColor Red; ^
    Write-Host '请尝试以管理员身份运行此文件' -ForegroundColor Yellow; ^
}"

if exist "摄影分类统计_饼图.xlsx" (
    echo.
    echo ========================================
    echo ✓ 成功创建Excel文件！
    echo ========================================
    echo.
    echo 文件名: 摄影分类统计_饼图.xlsx
    echo.
    echo 文件包含:
    echo ✓ 完整的数据表格 ^(TOP 10分类^)
    echo ✓ 饼图 ^(带百分比标签^)
    echo ✓ 图例和统计摘要
    echo.
    echo TOP 5 分类:
    echo 1. 人像摄影: 534次 ^(26.9%%^)
    echo 2. 婚礼摄影: 145次 ^(7.3%%^)
    echo 3. 其他: 136次 ^(6.9%%^)
    echo 4. 商业摄影: 118次 ^(5.9%%^)
    echo 5. 儿童摄影: 117次 ^(5.9%%^)
    echo.
    echo 是否立即打开Excel文件查看？^(Y/N^)
    set /p choice=
    if /i "!choice!"=="Y" (
        start "" "摄影分类统计_饼图.xlsx"
    )
) else (
    echo.
    echo ========================================
    echo ✗ 文件创建失败
    echo ========================================
    echo.
    echo 可能的解决方案:
    echo 1. 右键点击此文件，选择"以管理员身份运行"
    echo 2. 确保已安装Microsoft Excel
    echo 3. 运行"权限检查和修复.bat"进行诊断
    echo 4. 临时关闭防病毒软件
)

echo.
pause
