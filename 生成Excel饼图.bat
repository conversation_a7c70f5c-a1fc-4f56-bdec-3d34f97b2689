@echo off
chcp 65001 >nul
echo ========================================
echo 正在生成摄影分类统计Excel文件（含饼图）
echo ========================================
echo.

echo [1/5] 启动Excel应用程序...
powershell -ExecutionPolicy Bypass -Command ^
"try { ^
    $excel = New-Object -ComObject Excel.Application; ^
    $excel.Visible = $false; ^
    $excel.DisplayAlerts = $false; ^
    Write-Host '[成功] Excel应用程序已启动' -ForegroundColor Green; ^
    $workbook = $excel.Workbooks.Add(); ^
    $worksheet = $workbook.ActiveSheet; ^
    $worksheet.Name = '摄影分类统计'; ^
    Write-Host '[成功] 工作簿已创建' -ForegroundColor Green; ^
    $worksheet.Cells.Item(1, 1) = '摄影分类统计分析（拆分版）'; ^
    $worksheet.Cells.Item(1, 1).Font.Size = 16; ^
    $worksheet.Cells.Item(1, 1).Font.Bold = $true; ^
    $worksheet.Cells.Item(3, 1) = '分类'; ^
    $worksheet.Cells.Item(3, 2) = '计数'; ^
    $worksheet.Cells.Item(3, 1).Font.Bold = $true; ^
    $worksheet.Cells.Item(3, 2).Font.Bold = $true; ^
    $worksheet.Cells.Item(4, 1) = '人像摄影'; $worksheet.Cells.Item(4, 2) = 534; ^
    $worksheet.Cells.Item(5, 1) = '婚礼摄影'; $worksheet.Cells.Item(5, 2) = 145; ^
    $worksheet.Cells.Item(6, 1) = '其他'; $worksheet.Cells.Item(6, 2) = 136; ^
    $worksheet.Cells.Item(7, 1) = '商业摄影'; $worksheet.Cells.Item(7, 2) = 118; ^
    $worksheet.Cells.Item(8, 1) = '儿童摄影'; $worksheet.Cells.Item(8, 2) = 117; ^
    $worksheet.Cells.Item(9, 1) = '风景摄影'; $worksheet.Cells.Item(9, 2) = 99; ^
    $worksheet.Cells.Item(10, 1) = '婚礼跟拍'; $worksheet.Cells.Item(10, 2) = 69; ^
    $worksheet.Cells.Item(11, 1) = '风光人文'; $worksheet.Cells.Item(11, 2) = 62; ^
    Write-Host '[成功] 数据已添加' -ForegroundColor Green; ^
    $worksheet.Columns('A:A').ColumnWidth = 15; ^
    $worksheet.Columns('B:B').ColumnWidth = 8; ^
    $dataRange = $worksheet.Range('A3:B11'); ^
    $chartObject = $worksheet.Shapes.AddChart(); ^
    $chart = $chartObject.Chart; ^
    $chart.ChartType = 5; ^
    $chart.SetSourceData($dataRange); ^
    $chart.HasTitle = $true; ^
    $chart.ChartTitle.Text = '摄影分类分布统计'; ^
    $chartObject.Left = 250; ^
    $chartObject.Top = 50; ^
    $chartObject.Width = 400; ^
    $chartObject.Height = 300; ^
    $chart.SeriesCollection(1).HasDataLabels = $true; ^
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true; ^
    Write-Host '[成功] 饼图已创建' -ForegroundColor Green; ^
    $outputFile = '摄影分类统计_饼图.xlsx'; ^
    $fullPath = Join-Path (Get-Location) $outputFile; ^
    if (Test-Path $outputFile) { Remove-Item $outputFile -Force }; ^
    $workbook.SaveAs($fullPath); ^
    Write-Host '[成功] 文件已保存: $outputFile' -ForegroundColor Green; ^
    $workbook.Close($false); ^
    $excel.Quit(); ^
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null; ^
    if (Test-Path $outputFile) { ^
        $fileInfo = Get-Item $outputFile; ^
        Write-Host ''; ^
        Write-Host '=== 任务完成! ===' -ForegroundColor Cyan; ^
        Write-Host '文件名: $($fileInfo.Name)' -ForegroundColor White; ^
        Write-Host '大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB' -ForegroundColor White; ^
        Write-Host ''; ^
        Write-Host '文件包含:' -ForegroundColor Yellow; ^
        Write-Host '✓ 摄影分类数据表格' -ForegroundColor White; ^
        Write-Host '✓ 饼图（显示百分比）' -ForegroundColor White; ^
        Write-Host '✓ TOP 8 摄影分类' -ForegroundColor White; ^
        Write-Host ''; ^
        Write-Host 'TOP 5 分类:' -ForegroundColor Yellow; ^
        Write-Host '1. 人像摄影: 534次 (26.9%%)' -ForegroundColor White; ^
        Write-Host '2. 婚礼摄影: 145次 (7.3%%)' -ForegroundColor White; ^
        Write-Host '3. 其他: 136次 (6.9%%)' -ForegroundColor White; ^
        Write-Host '4. 商业摄影: 118次 (5.9%%)' -ForegroundColor White; ^
        Write-Host '5. 儿童摄影: 117次 (5.9%%)' -ForegroundColor White; ^
    } else { ^
        Write-Host '[错误] 文件未找到!' -ForegroundColor Red; ^
    } ^
} catch { ^
    Write-Host '[错误] $($_.Exception.Message)' -ForegroundColor Red; ^
    try { ^
        if ($workbook) { $workbook.Close($false) }; ^
        if ($excel) { $excel.Quit(); [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null }; ^
    } catch {} ^
}"

echo.
echo ========================================
echo 脚本执行完成
echo ========================================
echo.
echo 如果成功，您应该看到 "摄影分类统计_饼图.xlsx" 文件
echo 该文件包含完整的数据表格和饼图
echo.
pause
