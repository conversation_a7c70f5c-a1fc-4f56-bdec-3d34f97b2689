@echo off
echo 正在创建包含饼图的Excel文件...

powershell.exe -ExecutionPolicy Bypass -Command "& {
    try {
        Write-Host '启动Excel...' -ForegroundColor Yellow
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        
        $workbook = $excel.Workbooks.Add()
        $worksheet = $workbook.ActiveSheet
        $worksheet.Name = '摄影分类统计'
        
        Write-Host '添加数据...' -ForegroundColor Yellow
        $worksheet.Cells.Item(1, 1) = '摄影分类统计分析'
        $worksheet.Cells.Item(1, 1).Font.Size = 16
        $worksheet.Cells.Item(1, 1).Font.Bold = $true
        
        $worksheet.Cells.Item(3, 1) = '分类'
        $worksheet.Cells.Item(3, 2) = '计数'
        $worksheet.Cells.Item(3, 1).Font.Bold = $true
        $worksheet.Cells.Item(3, 2).Font.Bold = $true
        
        $worksheet.Cells.Item(4, 1) = '人像摄影'
        $worksheet.Cells.Item(4, 2) = 534
        $worksheet.Cells.Item(5, 1) = '婚礼摄影'
        $worksheet.Cells.Item(5, 2) = 145
        $worksheet.Cells.Item(6, 1) = '其他'
        $worksheet.Cells.Item(6, 2) = 136
        $worksheet.Cells.Item(7, 1) = '商业摄影'
        $worksheet.Cells.Item(7, 2) = 118
        $worksheet.Cells.Item(8, 1) = '儿童摄影'
        $worksheet.Cells.Item(8, 2) = 117
        $worksheet.Cells.Item(9, 1) = '风景摄影'
        $worksheet.Cells.Item(9, 2) = 99
        
        $worksheet.Columns('A:A').ColumnWidth = 15
        $worksheet.Columns('B:B').ColumnWidth = 8
        
        Write-Host '创建饼图...' -ForegroundColor Yellow
        $dataRange = $worksheet.Range('A3:B9')
        $chartObject = $worksheet.Shapes.AddChart()
        $chart = $chartObject.Chart
        $chart.ChartType = 5
        $chart.SetSourceData($dataRange)
        $chart.HasTitle = $true
        $chart.ChartTitle.Text = '摄影分类分布'
        $chartObject.Left = 200
        $chartObject.Top = 50
        $chartObject.Width = 400
        $chartObject.Height = 300
        $chart.SeriesCollection(1).HasDataLabels = $true
        $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
        
        Write-Host '保存文件...' -ForegroundColor Yellow
        $outputFile = '摄影分类饼图.xlsx'
        $fullPath = Join-Path (Get-Location) $outputFile
        if (Test-Path $outputFile) { Remove-Item $outputFile -Force }
        $workbook.SaveAs($fullPath)
        
        $workbook.Close($false)
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        
        Write-Host '成功创建: $outputFile' -ForegroundColor Green
        if (Test-Path $outputFile) {
            Write-Host '文件已确认存在' -ForegroundColor Green
        }
    } catch {
        Write-Host '错误: $_' -ForegroundColor Red
    }
}"

echo 脚本执行完成
pause
