# Create a new Excel workbook with analysis results
param(
    [string]$SourceFile = "",
    [string]$OutputFile = "D列统计分析结果.xlsx"
)

# Find source Excel file
if ($SourceFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" -and $_.Name -ne $OutputFile }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No source Excel files found" -ForegroundColor Red
        exit 1
    }
    $SourceFile = $excelFiles[0].Name
}

try {
    Write-Host "Reading source file: $SourceFile" -ForegroundColor Green
    
    # Read existing analysis results from CSV
    if (Test-Path "column_d_results.csv") {
        Write-Host "Using existing analysis results from CSV..." -ForegroundColor Yellow
        $csvData = Import-Csv "column_d_results.csv"
        
        # Create new Excel workbook
        $excel = New-Object -ComObject Excel.Application
        $excel.Visible = $false
        $excel.DisplayAlerts = $false
        
        $workbook = $excel.Workbooks.Add()
        $worksheet = $workbook.ActiveSheet
        $worksheet.Name = "D列统计分析"
        
        Write-Host "Creating analysis workbook..." -ForegroundColor Green
        
        # Add title
        $worksheet.Cells.Item(1, 1) = "D列摄影类型统计分析报告"
        $worksheet.Cells.Item(1, 1).Font.Size = 16
        $worksheet.Cells.Item(1, 1).Font.Bold = $true
        
        # Add metadata
        $worksheet.Cells.Item(3, 1) = "分析时间:"
        $worksheet.Cells.Item(3, 2) = (Get-Date).ToString("yyyy年MM月dd日 HH:mm:ss")
        
        $worksheet.Cells.Item(4, 1) = "数据来源:"
        $worksheet.Cells.Item(4, 2) = $SourceFile
        
        $worksheet.Cells.Item(5, 1) = "总记录数:"
        $worksheet.Cells.Item(5, 2) = $csvData.Count
        
        # Add table headers
        $headerRow = 8
        $worksheet.Cells.Item($headerRow, 1) = "排名"
        $worksheet.Cells.Item($headerRow, 2) = "摄影类型"
        $worksheet.Cells.Item($headerRow, 3) = "数量"
        $worksheet.Cells.Item($headerRow, 4) = "占比(%)"
        
        # Format headers
        for ($col = 1; $col -le 4; $col++) {
            $worksheet.Cells.Item($headerRow, $col).Font.Bold = $true
            $worksheet.Cells.Item($headerRow, $col).Interior.Color = 15123099
        }
        
        # Add data
        $currentRow = $headerRow + 1
        $rank = 1
        
        foreach ($row in $csvData) {
            $worksheet.Cells.Item($currentRow, 1) = $rank
            $worksheet.Cells.Item($currentRow, 2) = $row.Value
            $worksheet.Cells.Item($currentRow, 3) = [int]$row.Count
            $worksheet.Cells.Item($currentRow, 4) = [double]$row.Percentage
            
            # Highlight top 10
            if ($rank -le 10) {
                for ($col = 1; $col -le 4; $col++) {
                    $worksheet.Cells.Item($currentRow, $col).Interior.Color = 16777164
                }
            }
            
            $currentRow++
            $rank++
        }
        
        # Auto-fit columns
        $worksheet.Columns("A:A").ColumnWidth = 8
        $worksheet.Columns("B:B").ColumnWidth = 45
        $worksheet.Columns("C:C").ColumnWidth = 10
        $worksheet.Columns("D:D").ColumnWidth = 12
        
        # Save workbook
        $fullPath = Join-Path (Get-Location) $OutputFile
        $workbook.SaveAs($fullPath)
        $workbook.Close($false)
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        
        Write-Host "Successfully created analysis workbook: $OutputFile" -ForegroundColor Green
        
        # Show summary
        Write-Host "`nAnalysis Summary:" -ForegroundColor Cyan
        Write-Host "Total records: $($csvData.Count)" -ForegroundColor Yellow
        Write-Host "Output file: $OutputFile" -ForegroundColor Yellow
        
        # Show top 5
        Write-Host "`nTOP 5 Photography Types:" -ForegroundColor Cyan
        $top5 = $csvData | Select-Object -First 5
        $rank = 1
        foreach ($item in $top5) {
            Write-Host "  $rank. $($item.Value): $($item.Count) ($($item.Percentage)%)" -ForegroundColor White
            $rank++
        }
        
    } else {
        Write-Host "Error: Analysis results CSV file not found. Please run the analysis first." -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}

Write-Host "`nTask completed!" -ForegroundColor Green
