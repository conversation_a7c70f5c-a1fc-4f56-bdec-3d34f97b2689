# Simple Excel file creation with pie chart
try {
    Write-Host "Starting Excel application..." -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $true  # Make visible to see progress
    $excel.DisplayAlerts = $false
    
    Write-Host "Creating workbook..." -ForegroundColor Yellow
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "分类统计"
    
    Write-Host "Adding title..." -ForegroundColor Yellow
    $worksheet.Cells.Item(1, 1) = "摄影分类统计分析"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    Write-Host "Adding data..." -ForegroundColor Yellow
    
    # Add headers
    $worksheet.Cells.Item(3, 1) = "分类"
    $worksheet.Cells.Item(3, 2) = "计数"
    $worksheet.Cells.Item(3, 1).Font.Bold = $true
    $worksheet.Cells.Item(3, 2).Font.Bold = $true
    
    # Add data
    $worksheet.Cells.Item(4, 1) = "人像摄影"
    $worksheet.Cells.Item(4, 2) = 534
    
    $worksheet.Cells.Item(5, 1) = "婚礼摄影"
    $worksheet.Cells.Item(5, 2) = 145
    
    $worksheet.Cells.Item(6, 1) = "其他"
    $worksheet.Cells.Item(6, 2) = 136
    
    $worksheet.Cells.Item(7, 1) = "商业摄影"
    $worksheet.Cells.Item(7, 2) = 118
    
    $worksheet.Cells.Item(8, 1) = "儿童摄影"
    $worksheet.Cells.Item(8, 2) = 117
    
    $worksheet.Cells.Item(9, 1) = "风景摄影"
    $worksheet.Cells.Item(9, 2) = 99
    
    $worksheet.Cells.Item(10, 1) = "婚礼跟拍"
    $worksheet.Cells.Item(10, 2) = 69
    
    $worksheet.Cells.Item(11, 1) = "风光人文"
    $worksheet.Cells.Item(11, 2) = 62
    
    $worksheet.Cells.Item(12, 1) = "视频制作"
    $worksheet.Cells.Item(12, 2) = 52
    
    $worksheet.Cells.Item(13, 1) = "儿童亲子"
    $worksheet.Cells.Item(13, 2) = 40
    
    Write-Host "Formatting columns..." -ForegroundColor Yellow
    $worksheet.Columns("A:A").ColumnWidth = 20
    $worksheet.Columns("B:B").ColumnWidth = 10
    
    Write-Host "Creating chart..." -ForegroundColor Yellow
    
    # Select data range
    $dataRange = $worksheet.Range("A3:B13")
    
    # Create chart
    $chartObject = $worksheet.Shapes.AddChart()
    $chart = $chartObject.Chart
    
    # Set chart type to pie
    $chart.ChartType = 5  # xlPie
    
    # Set data source
    $chart.SetSourceData($dataRange)
    
    # Set chart title
    $chart.HasTitle = $true
    $chart.ChartTitle.Text = "摄影分类分布"
    
    # Position chart
    $chartObject.Left = 250
    $chartObject.Top = 50
    $chartObject.Width = 400
    $chartObject.Height = 300
    
    # Add data labels
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    
    Write-Host "Saving file..." -ForegroundColor Yellow
    
    # Save file
    $outputFile = "摄影分类统计_饼图.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    $workbook.SaveAs($fullPath)
    
    Write-Host "File saved successfully: $outputFile" -ForegroundColor Green
    
    Write-Host "`nChart created with TOP 10 categories:" -ForegroundColor Cyan
    Write-Host "1. 人像摄影: 534 (26.9%)" -ForegroundColor White
    Write-Host "2. 婚礼摄影: 145 (7.3%)" -ForegroundColor White
    Write-Host "3. 其他: 136 (6.9%)" -ForegroundColor White
    Write-Host "4. 商业摄影: 118 (5.9%)" -ForegroundColor White
    Write-Host "5. 儿童摄影: 117 (5.9%)" -ForegroundColor White
    
    Write-Host "`nExcel file is open for your review." -ForegroundColor Green
    Write-Host "You can save and close it when ready." -ForegroundColor Green
    
    # Don't close automatically - let user review
    # $workbook.Close($false)
    # $excel.Quit()
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
}

Write-Host "`nScript completed!" -ForegroundColor Green
