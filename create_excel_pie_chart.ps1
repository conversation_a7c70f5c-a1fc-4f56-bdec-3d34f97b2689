# Create Excel file with pie chart directly from analysis
try {
    Write-Host "Creating Excel file with pie chart..." -ForegroundColor Green
    
    # Create Excel application
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Create new workbook
    $workbook = $excel.Workbooks.Add()
    $worksheet = $workbook.ActiveSheet
    $worksheet.Name = "摄影分类统计"
    
    Write-Host "Adding data and creating chart..." -ForegroundColor Yellow
    
    # Add title
    $worksheet.Cells.Item(1, 1) = "D列摄影分类统计分析（拆分版）"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    # Add metadata
    $worksheet.Cells.Item(3, 1) = "分析说明："
    $worksheet.Cells.Item(3, 2) = "每个条目中的多个分类都单独计算"
    
    $worksheet.Cells.Item(4, 1) = "分析时间："
    $worksheet.Cells.Item(4, 2) = (Get-Date).ToString("yyyy年MM月dd日 HH:mm:ss")
    
    $worksheet.Cells.Item(5, 1) = "总分类计数："
    $worksheet.Cells.Item(5, 2) = 1986
    
    $worksheet.Cells.Item(6, 1) = "唯一分类数："
    $worksheet.Cells.Item(6, 2) = 26
    
    # Prepare chart data (TOP 12 for better visualization)
    $chartData = @(
        @("人像摄影", 534),
        @("婚礼摄影", 145),
        @("其他", 136),
        @("商业摄影", 118),
        @("儿童摄影", 117),
        @("风景摄影", 99),
        @("婚礼跟拍（约拍）", 69),
        @("风光/人文", 62),
        @("视频制作", 52),
        @("儿童摄影（亲子）", 40),
        @("未分类", 37),
        @("自媒体VLOG", 28)
    )
    
    # Add chart data to worksheet
    $chartStartRow = 9
    $worksheet.Cells.Item($chartStartRow, 1) = "摄影分类"
    $worksheet.Cells.Item($chartStartRow, 2) = "计数"
    $worksheet.Cells.Item($chartStartRow, 3) = "占比(%)"
    
    # Format headers
    $headerRange = $worksheet.Range("A$chartStartRow" + ":C$chartStartRow")
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # Light blue
    
    # Add data
    $currentRow = $chartStartRow + 1
    $totalCount = 1986
    
    foreach ($item in $chartData) {
        $category = $item[0]
        $count = $item[1]
        $percentage = [math]::Round(($count / $totalCount) * 100, 2)
        
        $worksheet.Cells.Item($currentRow, 1) = $category
        $worksheet.Cells.Item($currentRow, 2) = $count
        $worksheet.Cells.Item($currentRow, 3) = $percentage
        
        $currentRow++
    }
    
    # Auto-fit columns
    $worksheet.Columns("A:A").ColumnWidth = 25
    $worksheet.Columns("B:B").ColumnWidth = 10
    $worksheet.Columns("C:C").ColumnWidth = 12
    
    Write-Host "Creating pie chart..." -ForegroundColor Yellow
    
    # Select data range for chart (categories and counts)
    $dataRange = $worksheet.Range("A$($chartStartRow + 1):B$($currentRow - 1)")
    
    # Create pie chart
    $chartObject = $worksheet.Shapes.AddChart2(201, 5)  # xlPie = 5
    $chart = $chartObject.Chart
    
    # Set chart data source
    $chart.SetSourceData($dataRange)
    
    # Configure chart
    $chart.ChartTitle.Text = "摄影分类分布统计"
    $chart.ChartTitle.Font.Size = 14
    $chart.ChartTitle.Font.Bold = $true
    
    # Position and size chart
    $chartObject.Left = $worksheet.Cells.Item(9, 5).Left
    $chartObject.Top = $worksheet.Cells.Item(9, 5).Top
    $chartObject.Width = 500
    $chartObject.Height = 400
    
    # Format chart
    $chart.HasLegend = $true
    $chart.Legend.Position = -4152  # xlLegendPositionRight
    $chart.Legend.Font.Size = 10
    
    # Add data labels with percentages
    $chart.SeriesCollection(1).HasDataLabels = $true
    $chart.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chart.SeriesCollection(1).DataLabels.ShowCategoryName = $false
    $chart.SeriesCollection(1).DataLabels.ShowValue = $false
    $chart.SeriesCollection(1).DataLabels.Font.Size = 10
    
    # Format chart colors (optional - Excel will use default colors)
    try {
        $chart.SeriesCollection(1).Points(1).Format.Fill.ForeColor.RGB = 255 + (0 * 256) + (0 * 65536)      # Red
        $chart.SeriesCollection(1).Points(2).Format.Fill.ForeColor.RGB = 0 + (128 * 256) + (255 * 65536)    # Blue
        $chart.SeriesCollection(1).Points(3).Format.Fill.ForeColor.RGB = 0 + (255 * 256) + (0 * 65536)      # Green
        $chart.SeriesCollection(1).Points(4).Format.Fill.ForeColor.RGB = 255 + (165 * 256) + (0 * 65536)    # Orange
        $chart.SeriesCollection(1).Points(5).Format.Fill.ForeColor.RGB = 128 + (0 * 256) + (128 * 65536)    # Purple
    } catch {
        # If color formatting fails, continue with default colors
        Write-Host "Using default chart colors" -ForegroundColor Gray
    }
    
    # Add summary table below the data
    $summaryStartRow = $currentRow + 2
    $worksheet.Cells.Item($summaryStartRow, 1) = "TOP 5 分类汇总"
    $worksheet.Cells.Item($summaryStartRow, 1).Font.Size = 12
    $worksheet.Cells.Item($summaryStartRow, 1).Font.Bold = $true
    
    $summaryRow = $summaryStartRow + 1
    for ($i = 0; $i -lt 5; $i++) {
        $category = $chartData[$i][0]
        $count = $chartData[$i][1]
        $percentage = [math]::Round(($count / $totalCount) * 100, 2)
        
        $worksheet.Cells.Item($summaryRow + $i, 1) = "$($i + 1). $category"
        $worksheet.Cells.Item($summaryRow + $i, 2) = "$count ($percentage%)"
    }
    
    Write-Host "Saving Excel file..." -ForegroundColor Yellow
    
    # Save workbook
    $outputFile = "摄影分类统计分析_含饼图.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    $workbook.SaveAs($fullPath)
    
    Write-Host "Successfully created Excel file: $outputFile" -ForegroundColor Green
    
    # Display summary
    Write-Host "`nFile Details:" -ForegroundColor Cyan
    Write-Host "Output file: $outputFile" -ForegroundColor White
    Write-Host "Chart type: Pie chart with percentages" -ForegroundColor White
    Write-Host "Data points: TOP 12 categories" -ForegroundColor White
    Write-Host "Total categories analyzed: $totalCount instances" -ForegroundColor White
    
    Write-Host "`nTOP 5 Categories in Chart:" -ForegroundColor Cyan
    for ($i = 0; $i -lt 5; $i++) {
        $category = $chartData[$i][0]
        $count = $chartData[$i][1]
        $percentage = [math]::Round(($count / $totalCount) * 100, 2)
        Write-Host "  $($i + 1). $category: $count ($percentage%)" -ForegroundColor White
    }
    
    # Close Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
    
    try {
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {
        Write-Host "Cleanup error (can be ignored): $($_.Exception.Message)" -ForegroundColor Gray
    }
    
    exit 1
}

Write-Host "`nTask completed successfully!" -ForegroundColor Green
Write-Host "You can now open '$outputFile' to view the pie chart." -ForegroundColor Green
