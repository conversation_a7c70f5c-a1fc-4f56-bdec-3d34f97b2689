# PowerShell script to analyze D column with split categories and create Excel with pie chart
param(
    [string]$ExcelFile = ""
)

# Find Excel file
if ($ExcelFile -eq "") {
    $excelFiles = Get-ChildItem -Path "." -Filter "*.xlsx" | Where-Object { $_.Name -notlike "~*" -and $_.Name -notlike "*分析*" }
    if ($excelFiles.Count -eq 0) {
        Write-Host "Error: No Excel files found" -ForegroundColor Red
        exit 1
    }
    $ExcelFile = $excelFiles[0].Name
}

try {
    Write-Host "Opening Excel file: $ExcelFile" -ForegroundColor Green
    
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    $workbook = $excel.Workbooks.Open((Resolve-Path $ExcelFile).Path)
    $originalSheet = $workbook.ActiveSheet
    
    # Read data from column D
    $lastRow = $originalSheet.UsedRange.Rows.Count
    $rawData = @()
    
    Write-Host "Reading data from column D..." -ForegroundColor Yellow
    for ($row = 2; $row -le $lastRow; $row++) {
        $cellValue = $originalSheet.Cells.Item($row, 4).Value2
        if ($cellValue -ne $null -and $cellValue.ToString().Trim() -ne "") {
            $rawData += $cellValue.ToString().Trim()
        }
    }
    
    Write-Host "Total raw entries: $($rawData.Count)" -ForegroundColor Yellow
    
    # Split categories and count
    $categoryCount = @{}
    $totalCategories = 0
    
    Write-Host "Processing and splitting categories..." -ForegroundColor Yellow
    
    foreach ($entry in $rawData) {
        # Split by common separators: +, 、, ，, and other delimiters
        $categories = $entry -split '[+、，,]' | ForEach-Object { $_.Trim() } | Where-Object { $_ -ne "" }
        
        foreach ($category in $categories) {
            # Clean up category name
            $cleanCategory = $category.Trim()
            
            # Skip empty or very short categories
            if ($cleanCategory.Length -gt 1) {
                if ($categoryCount.ContainsKey($cleanCategory)) {
                    $categoryCount[$cleanCategory]++
                } else {
                    $categoryCount[$cleanCategory] = 1
                }
                $totalCategories++
            }
        }
    }
    
    Write-Host "Total individual categories counted: $totalCategories" -ForegroundColor Yellow
    Write-Host "Unique category types: $($categoryCount.Count)" -ForegroundColor Yellow
    
    # Sort results by count
    $sortedResults = $categoryCount.GetEnumerator() | Sort-Object Value -Descending
    
    # Close original workbook
    $workbook.Close($false)
    
    # Create new analysis workbook
    Write-Host "Creating new analysis workbook..." -ForegroundColor Green
    
    $newWorkbook = $excel.Workbooks.Add()
    $worksheet = $newWorkbook.ActiveSheet
    $worksheet.Name = "分类统计分析"
    
    # Add title and metadata
    $worksheet.Cells.Item(1, 1) = "D列摄影分类统计分析（拆分统计）"
    $worksheet.Cells.Item(1, 1).Font.Size = 16
    $worksheet.Cells.Item(1, 1).Font.Bold = $true
    
    $worksheet.Cells.Item(3, 1) = "分析说明："
    $worksheet.Cells.Item(3, 2) = "每个条目中的多个分类都单独计算"
    
    $worksheet.Cells.Item(4, 1) = "分析时间："
    $worksheet.Cells.Item(4, 2) = (Get-Date).ToString("yyyy年MM月dd日 HH:mm:ss")
    
    $worksheet.Cells.Item(5, 1) = "数据来源："
    $worksheet.Cells.Item(5, 2) = $ExcelFile
    
    $worksheet.Cells.Item(6, 1) = "原始条目数："
    $worksheet.Cells.Item(6, 2) = $rawData.Count
    
    $worksheet.Cells.Item(7, 1) = "总分类计数："
    $worksheet.Cells.Item(7, 2) = $totalCategories
    
    $worksheet.Cells.Item(8, 1) = "唯一分类数："
    $worksheet.Cells.Item(8, 2) = $categoryCount.Count
    
    # Create data table
    $startRow = 11
    $worksheet.Cells.Item($startRow, 1) = "排名"
    $worksheet.Cells.Item($startRow, 2) = "摄影分类"
    $worksheet.Cells.Item($startRow, 3) = "计数"
    $worksheet.Cells.Item($startRow, 4) = "占比(%)"
    
    # Format headers
    $headerRange = $worksheet.Range("A$startRow" + ":D$startRow")
    $headerRange.Font.Bold = $true
    $headerRange.Interior.Color = 15123099  # Light blue
    $headerRange.Borders.LineStyle = 1
    
    # Add data
    $currentRow = $startRow + 1
    $rank = 1
    
    foreach ($item in $sortedResults) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        
        $worksheet.Cells.Item($currentRow, 1) = $rank
        $worksheet.Cells.Item($currentRow, 2) = $item.Key
        $worksheet.Cells.Item($currentRow, 3) = $item.Value
        $worksheet.Cells.Item($currentRow, 4) = $percentage
        
        # Format data row
        $dataRange = $worksheet.Range("A$currentRow" + ":D$currentRow")
        $dataRange.Borders.LineStyle = 1
        
        # Highlight top 10
        if ($rank -le 10) {
            $dataRange.Interior.Color = 16777164  # Light yellow
        }
        
        $currentRow++
        $rank++
    }
    
    # Auto-fit columns
    $worksheet.Columns("A:A").ColumnWidth = 8
    $worksheet.Columns("B:B").ColumnWidth = 35
    $worksheet.Columns("C:C").ColumnWidth = 10
    $worksheet.Columns("D:D").ColumnWidth = 12
    
    # Create pie chart for top categories
    Write-Host "Creating pie chart..." -ForegroundColor Yellow
    
    # Get top 15 categories for the chart (to avoid overcrowding)
    $top15 = $sortedResults | Select-Object -First 15
    
    # Prepare chart data range
    $chartStartRow = $currentRow + 3
    $worksheet.Cells.Item($chartStartRow, 6) = "图表数据 (TOP 15)"
    $worksheet.Cells.Item($chartStartRow, 6).Font.Bold = $true
    
    $chartDataRow = $chartStartRow + 1
    $worksheet.Cells.Item($chartDataRow, 6) = "分类"
    $worksheet.Cells.Item($chartDataRow, 7) = "计数"
    
    $chartCurrentRow = $chartDataRow + 1
    foreach ($item in $top15) {
        $worksheet.Cells.Item($chartCurrentRow, 6) = $item.Key
        $worksheet.Cells.Item($chartCurrentRow, 7) = $item.Value
        $chartCurrentRow++
    }
    
    # Create pie chart
    $chartRange = $worksheet.Range("F$chartDataRow" + ":G$($chartCurrentRow - 1)")
    $chartObject = $worksheet.Shapes.AddChart2(201, 5).Chart  # xlPie = 5, xl3DPie = -4102
    $chartObject.SetSourceData($chartRange)
    $chartObject.ChartTitle.Text = "摄影分类分布 (TOP 15)"
    $chartObject.ChartTitle.Font.Size = 14
    $chartObject.ChartTitle.Font.Bold = $true
    
    # Position and size the chart
    $chartObject.Parent.Left = $worksheet.Cells.Item(12, 6).Left
    $chartObject.Parent.Top = $worksheet.Cells.Item(12, 6).Top
    $chartObject.Parent.Width = 400
    $chartObject.Parent.Height = 300
    
    # Format chart
    $chartObject.HasLegend = $true
    $chartObject.Legend.Position = -4152  # xlLegendPositionRight
    $chartObject.PlotArea.Format.Fill.Visible = $false
    
    # Show data labels with percentages
    $chartObject.SeriesCollection(1).HasDataLabels = $true
    $chartObject.SeriesCollection(1).DataLabels.ShowPercentage = $true
    $chartObject.SeriesCollection(1).DataLabels.ShowCategoryName = $false
    $chartObject.SeriesCollection(1).DataLabels.ShowValue = $false
    
    Write-Host "Saving analysis workbook..." -ForegroundColor Yellow
    
    # Save the new workbook
    $outputFile = "D列分类统计分析_拆分版.xlsx"
    $fullPath = Join-Path (Get-Location) $outputFile
    $newWorkbook.SaveAs($fullPath)
    
    Write-Host "Successfully created analysis workbook: $outputFile" -ForegroundColor Green
    
    # Display summary
    Write-Host "`nAnalysis Summary:" -ForegroundColor Cyan
    Write-Host "Original entries: $($rawData.Count)" -ForegroundColor White
    Write-Host "Total category instances: $totalCategories" -ForegroundColor White
    Write-Host "Unique categories: $($categoryCount.Count)" -ForegroundColor White
    Write-Host "Output file: $outputFile" -ForegroundColor White
    
    Write-Host "`nTOP 10 Photography Categories:" -ForegroundColor Cyan
    $top10 = $sortedResults | Select-Object -First 10
    $displayRank = 1
    foreach ($item in $top10) {
        $percentage = [math]::Round(($item.Value / $totalCategories) * 100, 2)
        Write-Host "  $displayRank. $($item.Key): $($item.Value) ($percentage%)" -ForegroundColor White
        $displayRank++
    }
    
    # Close Excel
    $newWorkbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.ToString())" -ForegroundColor Red
    
    try {
        if ($newWorkbook) { $newWorkbook.Close($false) }
        if ($workbook) { $workbook.Close($false) }
        if ($excel) { 
            $excel.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
        }
    } catch {}
    
    exit 1
}

Write-Host "`nTask completed successfully!" -ForegroundColor Green
